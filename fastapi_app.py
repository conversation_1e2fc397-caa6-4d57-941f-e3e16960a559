# fastapi_app.py
from __future__ import annotations
import os, json, sqlite3, time, io
import re
from typing import List, Optional, Dict, Any
from fastapi import FastAPI, Depends, HTTPException, Query, Header
import matplotlib
from pydantic import BaseModel

# --- config ----------------------------------------------------------
DB_PATH = os.environ.get("NOC_DB_PATH", "./device_probe.sqlite3")
STATE_PATH = os.environ.get("NOC_STATE_PATH", "./.state/probe.json")
JOURNAL_PATH = os.environ.get("NOC_JOURNAL_PATH", "./daily_outages.jsonl")
API_KEY = os.environ.get("NOC_API_KEY",'gZXE+hbKJ4/M/G+Ii5sWlDhy6nV7TVcxL9gK93711ZM=')  # set to any string; if unset → open

def _get_db():
    # Read-only, shared cache; safe for concurrent readers
    uri = f"file:{DB_PATH}?mode=ro&cache=shared"
    conn = sqlite3.connect(uri, uri=True, check_same_thread=False)
    conn.row_factory = sqlite3.Row
    # Speed knobs for read-only
    conn.execute("PRAGMA journal_mode=WAL;")
    conn.execute("PRAGMA synchronous=OFF;")
    conn.execute("PRAGMA temp_store=MEMORY;")
    try:
        yield conn
    finally:
        conn.close()

def require_api_key(x_api_key: Optional[str] = Header(default=None)):
    if API_KEY and x_api_key != API_KEY:
        raise HTTPException(status_code=401, detail="invalid api key")

try:
    from config import settings as SETTINGS
except Exception:
    from config import Settings
    SETTINGS = Settings()

# --- models ----------------------------------------------------------
class Sample(BaseModel):
    ts: int
    ts_local: str
    olt_id: str
    device_name: Optional[str]
    fs_ip: Optional[str]
    fs_ifname: str
    fs_status: str
    fs_up: bool
    olt_up: bool
    in_bps: float
    out_bps: float
    has_traffic: bool
    dom_rx_dbm: Optional[float] = None
    dom_tx_dbm: Optional[float] = None
    dom_switch: Optional[str] = None
    summary: Optional[str] = None

class SeriesPoint(BaseModel):
    ts: int
    ts_local: str
    olt_id: str
    value_in_bps: Optional[float] = None
    value_out_bps: Optional[float] = None
    dom_rx_dbm: Optional[float] = None
    fs_status_last: Optional[str] = None
    samples: Optional[int] = None

class ONUState(BaseModel):
    device_id: str
    olt_id: str
    site_id: str
    first_offline_at: Optional[float]
    last_seen_poll: Optional[float]
    suspended: bool
    had_ticket: bool
    ticket_id: Optional[str] = None

# --- helpers ---------------------------------------------------------
def _table_cols(db: sqlite3.Connection, table: str) -> set[str]:
    rows = db.execute(f"PRAGMA table_info({table});").fetchall()
    return {r[1] for r in rows}  # r[1] == column name

def _make_note(
    *, error: str | None, up: bool,
    status: str, severity: str | None,
    rx: float | None, tx: float | None,
    dt_ms: int | None
) -> str:
    if error:
        if error.startswith("short_dt"):
            return f"Unreliable traffic sample ({dt_ms or 0}ms)"
        return f"Probe error: {error}"
    if not up:
        return f"Link {status or 'down'}"
    sev = (severity or "").lower()
    if sev == "caution" or sev == "warning":
        if rx is not None or tx is not None:
            return f"DOM caution (RX {rx if rx is not None else 'n/a'} dBm, TX {tx if tx is not None else 'n/a'} dBm)"
        return "DOM caution"
    if sev == "alarm":
        if rx is not None or tx is not None:
            return f"DOM warning (RX {rx if rx is not None else 'n/a'} dBm, TX {tx if tx is not None else 'n/a'} dBm)"
        return "DOM warning"
    return "OK"


# ---------- UI helpers ----------
def _shortuuid(u: str) -> str:
    return (u[:8] + "…" + u[-4:]) if u and len(u) > 14 else (u or "")

def _mbps(v: Any, digits: int = 1) -> float:
    try:
        return round(float(v) / 1_000_000.0, digits)
    except Exception:
        return 0.0

def _linklabel(oid: str) -> str:
    # optional: prettify link-... ids if you use them
    if oid.startswith("link-"):
        return oid.replace("link-", "").replace("-", " ").upper()
    return ""

class UiRow(BaseModel):
    device: str
    ip: str | None
    port: str
    status: str
    up: bool
    is_link: bool
    link_label: str | None
    in_mbps: float
    out_mbps: float
    rx_dbm: float | None
    tx_dbm: float | None
    severity: str | None          # normal | warning | alarm | None
    severity_rank: int            # 0 normal, 1 warning, 2 alarm, -1 unknown
    olt_id: str
    olt_id_short: str
    note: str                     # <-- new

def _linklabel_from_oid(oid: str) -> Optional[str]:
    """Turn 'link-wtn-25-off' -> 'WTN 25 → OFF'."""
    if not isinstance(oid, str) or not oid.startswith("link-"):
        return None
    m = re.match(r"^link-([a-z]+)-(\d+)-([a-z]+)$", oid.strip())
    if not m:
        return None
    a, port, b = m.groups()
    return f"{a.upper()} {port} \u2192 {b.upper()}"

# Build authoritative overlays from config.py
_OLT_INDEX: Dict[str, Dict[str, str]] = {}
for oid, meta in (getattr(SETTINGS, "OLT_PROBES", {}) or {}).items():
    _OLT_INDEX[str(oid)] = {
        "name": str(meta.get("name") or _shortuuid(str(oid))),
        "fs_ip": str(meta.get("fs_ip") or ""),
        "fs_ifname": str(meta.get("fs_uplink_ifname") or ""),
    }

_LINK_INDEX: Dict[str, Dict[str, str]] = {}
for lk in (getattr(SETTINGS, "FS_LINK_PROBES", []) or []):
    lid = str(lk.get("olt_id") or "")
    if not lid:
        # Fall back to a stable synthetic key if they didn't provide one
        fs_ip = str(lk.get("fs_ip") or "")
        ifname = str(lk.get("fs_ifname") or "")
        lid = f"link-{fs_ip.replace('.','_')}-{ifname.replace(' ','_')}"
    _LINK_INDEX[lid] = {
        "name": str(lk.get("name") or _linklabel_from_oid(lid) or lid),
        "fs_ip": str(lk.get("fs_ip") or ""),
        "fs_ifname": str(lk.get("fs_ifname") or ""),
    }
# --- app -------------------------------------------------------------
app = FastAPI(title="NOC Read-Only API", version="1.0.0")

@app.get("/healthz")
def healthz():
    ok = os.path.exists(DB_PATH)
    return {"ok": ok, "db": DB_PATH, "state": os.path.exists(STATE_PATH), "now": int(time.time())}

@app.get("/v1/samples/latest", dependencies=[Depends(require_api_key)], response_model=List[Sample])
def latest_samples(limit: int = Query(200, ge=1, le=2000), db: sqlite3.Connection = Depends(_get_db)):
    cols = _table_cols(db, "samples_raw")

    # choose sources per column
    fs_ifname_expr   = "r.fs_ifname"          if "fs_ifname"   in cols else "d.fs_ifname"
    fs_up_expr       = "r.fs_up"              if "fs_up"       in cols else "(CASE WHEN r.fs_status='up' THEN 1 ELSE 0 END)"
    olt_up_expr      = "r.olt_up"             if "olt_up"      in cols else "(CASE WHEN r.fs_status='up' THEN 1 ELSE 0 END)"
    has_traffic_expr = "r.has_traffic"        if "has_traffic" in cols else "(CASE WHEN (r.in_bps>0 OR r.out_bps>0) THEN 1 ELSE 0 END)"

    sql = f"""
    WITH latest_ts AS (SELECT MAX(ts) AS ts FROM samples_raw)
    SELECT r.ts,
           datetime(r.ts,'unixepoch','localtime') AS ts_local,
           r.olt_id,
           d.device_name,
           d.fs_ip,
           {fs_ifname_expr} AS fs_ifname,
           r.fs_status,
           {fs_up_expr}  AS fs_up,
           {olt_up_expr} AS olt_up,
           r.in_bps,
           r.out_bps,
           {has_traffic_expr} AS has_traffic,
           r.dom_rx_dbm,
           r.dom_tx_dbm,
           r.dom_switch,
           r.summary
    FROM samples_raw r
    LEFT JOIN devices d USING (olt_id)
    WHERE r.ts = (SELECT ts FROM latest_ts)
    ORDER BY d.device_name IS NULL, d.device_name, r.olt_id
    LIMIT :lim;
    """
    rows = db.execute(sql, {"lim": limit}).fetchall()
    return [Sample(**dict(rows[i])) for i in range(len(rows))]

from fastapi import Depends, Query

@app.get("/v1/ui/latest_table")
def ui_latest_table(
    limit: int = Query(400, ge=1, le=1000),
    only_links: bool = Query(False),
    db: sqlite3.Connection = Depends(_get_db),
    _: None = Depends(require_api_key),
):
    sql = """
    WITH latest AS (SELECT MAX(ts) AS ts FROM samples_raw)
    SELECT * FROM samples_raw r
    WHERE r.ts = (SELECT ts FROM latest)
    ORDER BY r.olt_id
    LIMIT :lim
    """
    rows = db.execute(sql, {"lim": limit}).fetchall()
    recs: List[Dict[str, Any]] = [dict(r) for r in rows]  # sqlite3.Row -> dict

    out: List[Dict[str, Any]] = []
    SEV = {"normal": 0, "warning": 1, "alarm": 2}

    for r in recs:
        oid = (r.get("olt_id") or "")
        is_link = oid.startswith("link-")

        # Pull what the DB has
        devname = (r.get("device_name") or "").strip()
        ip      = (r.get("fs_ip") or r.get("olt_ip") or "").strip()
        port    = (r.get("fs_ifname") or "").strip()

        # Overlay from config if missing/empty
        if is_link:
            meta = _LINK_INDEX.get(oid)
            if meta:
                if not devname:
                    devname = meta["name"]
                if not ip:
                    ip = meta["fs_ip"]
                if not port:
                    port = meta["fs_ifname"]
            # Derive pretty label if still no name
            link_label = _linklabel_from_oid(oid)
        else:
            meta = _OLT_INDEX.get(oid)
            if meta:
                if not devname:
                    devname = meta["name"]
                if not ip:
                    ip = meta["fs_ip"]
                if not port:
                    port = meta["fs_ifname"]
            link_label = None

        # Final device/link text: prefer explicit name, else pretty link, else short UUID
        device = devname or link_label or _shortuuid(oid)

        sev = (r.get("dom_switch") or "").lower() or None
        rank = SEV.get(sev or "", -1)

        rx = r.get("dom_rx_dbm"); rx = None if rx is None else float(rx)
        tx = r.get("dom_tx_dbm"); tx = None if tx is None else float(tx)
        up = bool(r.get("fs_up"))
        status = (r.get("fs_status") or "unknown")
        dt_ms = r.get("dt_ms"); dt_ms = None if dt_ms is None else int(dt_ms)

        note = _make_note(
            error=r.get("error"),
            up=up, status=status,
            severity=sev, rx=rx, tx=tx, dt_ms=dt_ms
        )

        out.append({
            "device": device,
            "ip": ip,
            "port": port,
            "status": status,
            "up": up,
            "is_link": is_link,
            "link_label": link_label,
            "in_mbps": _mbps(r.get("in_bps"), 1),
            "out_mbps": _mbps(r.get("out_bps"), 1),
            "rx_dbm": rx,
            "tx_dbm": tx,
            "severity": sev,
            "severity_rank": rank,
            "olt_id": oid,
            "olt_id_short": _shortuuid(oid),
            "note": note,
        })

    if only_links:
        out = [row for row in out if row["is_link"]]

    return out

@app.get("/v1/ui/chartjs/throughput")
def chartjs_throughput(
        since: int = Query(..., description="epoch seconds (inclusive)"),
        until: int = Query(..., description="epoch seconds (exclusive)"),
        olt_ids: str = Query("", description="comma-separated OLT IDs; empty = all"),
        agg: str = Query("5m", pattern="^(raw|5m|30m|60m|1h|auto)$"),
        dir: str = Query("in", pattern="^(in|out)$"),
        only_links: bool = Query(False),
        db: sqlite3.Connection = Depends(_get_db),
        _: None = Depends(require_api_key),
):
    def _query(agg_choice: str):
        ids = [s for s in (olt_ids.split(",") if olt_ids else []) if s]
        where = []
        params: Dict[str, Any] = {"since": since, "until": until}
        if ids:
            where.append(f"olt_id IN ({','.join(':' + f'k{i}' for i in range(len(ids)))})")
            params.update({f"k{i}": v for i, v in enumerate(ids)})
        if only_links:
            where.append("olt_id LIKE 'link-%'")
        where_sql = " AND ".join(where) if where else "1=1"

        val_col_in = "avg_in_bps"
        val_col_out = "avg_out_bps"
        val_col = val_col_in if dir == "in" else val_col_out

        if agg_choice == "raw":
            val_col_raw = "in_bps" if dir == "in" else "out_bps"
            sql = f"""
                SELECT ts, olt_id, {val_col_raw} AS v
                FROM samples_raw
                WHERE ts>=:since AND ts<:until AND {where_sql}
                ORDER BY ts ASC
            """
            return db.execute(sql, params).fetchall()

        if agg_choice in ("60m", "1h"):
            # try a native 60m table if you have it
            try:
                sql = f"""
                    SELECT bucket_ts AS ts, olt_id, {val_col} AS v
                    FROM agg_60m
                    WHERE bucket_ts>=:since AND bucket_ts<:until AND {where_sql}
                    ORDER BY bucket_ts ASC
                """
                rows = db.execute(sql, params).fetchall()
                if rows:
                    return rows
            except Exception:
                pass
            # fallback: group 5m into 60m on the fly
            sql = f"""
                SELECT (bucket_ts/3600)*3600 AS ts, olt_id, AVG({val_col}) AS v
                FROM agg_5m
                WHERE bucket_ts>=:since AND bucket_ts<:until AND {where_sql}
                GROUP BY ts, olt_id
                ORDER BY ts ASC
            """
            return db.execute(sql, params).fetchall()

        if agg_choice == "30m":
            # try native 30m first
            try:
                sql = f"""
                    SELECT bucket_ts AS ts, olt_id, {val_col} AS v
                    FROM agg_30m
                    WHERE bucket_ts>=:since AND bucket_ts<:until AND {where_sql}
                    ORDER BY bucket_ts ASC
                """
                rows = db.execute(sql, params).fetchall()
                if rows:
                    return rows
            except Exception:
                pass
            # fallback: group 5m into 30m
            sql = f"""
                SELECT (bucket_ts/1800)*1800 AS ts, olt_id, AVG({val_col}) AS v
                FROM agg_5m
                WHERE bucket_ts>=:since AND bucket_ts<:until AND {where_sql}
                GROUP BY ts, olt_id
                ORDER BY ts ASC
            """
            return db.execute(sql, params).fetchall()

        # default: 5m aggregates
        sql = f"""
            SELECT bucket_ts AS ts, olt_id, {val_col} AS v
            FROM agg_5m
            WHERE bucket_ts>=:since AND bucket_ts<:until AND {where_sql}
            ORDER BY bucket_ts ASC
        """
        return db.execute(sql, params).fetchall()

    # auto-agg helper
    if agg == "auto":
        span = until - since
        if span <= 8 * 3600:  # <= 8h
            agg = "5m"
        elif span <= 48 * 3600:  # <= 2d
            agg = "30m"
        else:
            agg = "60m"

    rows = _query(agg)
    used_agg = agg
    if not rows and agg != "raw":
        rows = _query("raw")
        used_agg = "raw"

    # try requested agg, then fall back to raw if empty
    rows = _query(agg)
    used_agg = agg
    if not rows and agg != "raw":
        rows = _query("raw")
        used_agg = "raw"

    # Build labels and datasets in Mbps
    by_ts: Dict[int, Dict[str, float]] = {}
    seen_ids: set[str] = set()
    for r in rows:
        ts = int(r["ts"])
        oid = r["olt_id"]
        seen_ids.add(oid)
        by_ts.setdefault(ts, {})[oid] = _mbps(r["v"], 1)

    labels = [time.strftime("%Y-%m-%d %H:%M", time.localtime(ts)) for ts in sorted(by_ts.keys())]
    datasets = []
    for oid in sorted(seen_ids):
        data = [by_ts.get(ts, {}).get(oid, None) for ts in sorted(by_ts.keys())]
        name = _label_for_series(oid)  # prefer configured/device names
        datasets.append({"label": name, "data": data, "spanGaps": True})

    title = f"Throughput ({'Mbps in' if dir=='in' else 'Mbps out'})"
    subtitle = f"{'links only; ' if only_links else ''}{used_agg} data; points={len(labels)}"

    return {
        "type": "line",
        "data": {"labels": labels, "datasets": datasets},
        "options": {
            "responsive": True,
            "interaction": {"mode": "index", "intersect": False},
            "plugins": {
                "title": {"display": True, "text": title},
                "subtitle": {"display": True, "text": subtitle},
                "legend": {"position": "bottom"},
            },
            "scales": {
                "y": {"title": {"display": True, "text": "Mbps"}},
                "x": {"title": {"display": True, "text": "Time (local)"}},
            },
        },
    }


from fastapi.responses import StreamingResponse

@app.get("/v1/ui/chart/throughput.png")
def png_throughput(
    since: int = Query(...),
    until: int = Query(...),
    olt_ids: str = Query(""),
    agg: str = Query("5m", pattern="^(raw|5m|30m)$"),
    dir: str = Query("in", pattern="^(in|out)$"),
    only_links: bool = Query(False),
    db: sqlite3.Connection = Depends(_get_db),
    _: None = Depends(require_api_key),
):
    import matplotlib.pyplot as plt
    import io as _io

    cfg = chartjs_throughput(since=since, until=until, olt_ids=olt_ids, agg=agg, dir=dir, only_links=only_links, db=db)  # reuse query
    labels = cfg["data"]["labels"]
    datasets = cfg["data"]["datasets"]

    x = list(range(len(labels)))
    fig, ax = plt.subplots(figsize=(9, 3))
    for ds in datasets:
        ax.plot(x, ds["data"], label=ds["label"])
    ax.set_xticks(x[::max(1, len(x)//10)])
    ax.set_xticklabels([labels[i] for i in ax.get_xticks()], rotation=45, ha="right")
    ax.set_ylabel("Mbps")
    ax.set_xlabel("Time")
    ax.legend(loc="upper right")
    fig.tight_layout()

    buf = _io.BytesIO()
    fig.savefig(buf, format="png", dpi=120)
    plt.close(fig)
    buf.seek(0)
    return StreamingResponse(buf, media_type="image/png")


@app.get("/v1/samples/window", dependencies=[Depends(require_api_key)], response_model=List[SeriesPoint])
def window_samples(
    since: int = Query(..., description="epoch seconds (inclusive)"),
    until: int = Query(..., description="epoch seconds (exclusive)"),
    olt_id: Optional[str] = Query(None),
    only_links: bool = Query(False),
    agg: str = Query("5m", pattern="^(raw|5m|30m)$"),
    db: sqlite3.Connection = Depends(_get_db),
):
    filt = []
    params: Dict[str, Any] = {"since": since, "until": until}
    if olt_id:
        filt.append("olt_id = :oid")
        params["oid"] = olt_id
    if only_links:
        filt.append("olt_id LIKE 'link-%'")
    where = " AND ".join(filt) if filt else "1=1"

    if agg == "raw":
        sql = f"""
        SELECT ts as ts,
               datetime(ts,'unixepoch','localtime') AS ts_local,
               olt_id, in_bps AS value_in_bps, out_bps AS value_out_bps,
               dom_rx_dbm, NULL AS fs_status_last, NULL AS samples
        FROM samples_raw
        WHERE ts >= :since AND ts < :until AND {where}
        ORDER BY ts ASC;
        """
    elif agg == "30m":
        sql = f"""
        SELECT bucket_ts AS ts,
               datetime(bucket_ts,'unixepoch','localtime') AS ts_local,
               olt_id, avg_in_bps AS value_in_bps, avg_out_bps AS value_out_bps,
               avg_dom_rx_dbm AS dom_rx_dbm, fs_status_last, samples
        FROM agg_30m
        WHERE bucket_ts >= :since AND bucket_ts < :until AND {where}
        ORDER BY bucket_ts ASC;
        """
    else:  # 5m
        sql = f"""
        SELECT bucket_ts AS ts,
               datetime(bucket_ts,'unixepoch','localtime') AS ts_local,
               olt_id, avg_in_bps AS value_in_bps, avg_out_bps AS value_out_bps,
               avg_dom_rx_dbm AS dom_rx_dbm, fs_status_last, samples
        FROM agg_5m
        WHERE bucket_ts >= :since AND bucket_ts < :until AND {where}
        ORDER BY bucket_ts ASC;
        """
    rows = db.execute(sql, params).fetchall()
    return [SeriesPoint(**dict(r)) for r in rows]

@app.get("/v1/olts", dependencies=[Depends(require_api_key)])
def list_olts(db: sqlite3.Connection = Depends(_get_db)):
    rows = db.execute("SELECT olt_id, device_name, fs_ip, fs_ifname FROM devices ORDER BY device_name;").fetchall()
    return [dict(r) for r in rows]

@app.get("/v1/links", dependencies=[Depends(require_api_key)])
def list_links(db: sqlite3.Connection = Depends(_get_db)):
    rows = db.execute("""
        SELECT olt_id, device_name, fs_ip, fs_ifname
        FROM devices
        WHERE olt_id LIKE 'link-%'
        ORDER BY device_name;
    """).fetchall()
    return [dict(r) for r in rows]

@app.get("/v1/state/onus", dependencies=[Depends(require_api_key)], response_model=List[ONUState])
def current_onu_state():
    if not os.path.exists(STATE_PATH):
        return []
    with open(STATE_PATH, "r", encoding="utf-8") as f:
        state = json.load(f)
    out: List[ONUState] = []
    for dev_id, s in state.items():
        out.append(ONUState(
            device_id=dev_id,
            olt_id=s.get("olt_id") or "",
            site_id=s.get("site_id") or "",
            first_offline_at=s.get("first_offline_at"),
            last_seen_poll=s.get("last_seen_poll"),
            suspended=bool(s.get("suspended", False)),
            had_ticket=bool(s.get("had_ticket", False)),
            ticket_id=s.get("ticket_id"),
        ))
    return out

@app.get("/v1/journal/recent", dependencies=[Depends(require_api_key)])
def journal_recent(limit: int = Query(100, ge=1, le=2000)):
    if not os.path.exists(JOURNAL_PATH):
        return []
    # efficient tail of JSONL
    def _tail_jsonl(path: str, n: int) -> List[str]:
        with open(path, "rb") as f:
            f.seek(0, io.SEEK_END)
            size = f.tell()
            buf = bytearray()
            lines: List[str] = []
            chunk = 4096
            pos = size
            while pos > 0 and len(lines) < n + 1:
                take = min(chunk, pos)
                pos -= take
                f.seek(pos)
                buf[:0] = f.read(take)
                lines = buf.splitlines()
            return [ln.decode("utf-8") for ln in lines[-n:]]
    return [json.loads(s) for s in _tail_jsonl(JOURNAL_PATH, limit)]

# --- SWITCH SNAPSHOT & SPARKLINES --------------------------------------------
from statistics import mean

def _pct95(nums: list[float]) -> float:
    if not nums:
        return 0.0
    xs = sorted(float(x) for x in nums)
    idx = max(0, int(round(0.95 * (len(xs) - 1))))
    return xs[idx]

def _devices_on_switch(db: sqlite3.Connection, fs_ip: str) -> list[dict]:
    rows = db.execute(
        "SELECT olt_id, device_name, fs_ip, fs_ifname FROM devices WHERE fs_ip = :ip ORDER BY device_name;",
        {"ip": fs_ip},
    ).fetchall()
    return [dict(r) for r in rows]

def _label_for_series(oid: str) -> str:
    """
    Prefer configured names, then pretty link label, then short UUID.
    """
    if isinstance(oid, str) and oid.startswith("link-"):
        return (_LINK_INDEX.get(oid, {}) or {}).get("name") \
               or _linklabel_from_oid(oid) \
               or _shortuuid(oid)
    meta = _OLT_INDEX.get(str(oid))
    if meta and meta.get("name"):
        return meta["name"]
    return _shortuuid(str(oid))


def _latest_rows_for_olts(db: sqlite3.Connection, olt_ids: list[str]) -> list[dict]:
    if not olt_ids:
        return []
    placeholders = ",".join(f":k{i}" for i in range(len(olt_ids)))
    sql = f"""
    WITH latest AS (SELECT MAX(ts) AS ts FROM samples_raw)
    SELECT * FROM samples_raw
    WHERE ts = (SELECT ts FROM latest) AND olt_id IN ({placeholders})
    """
    params = {f"k{i}": oid for i, oid in enumerate(olt_ids)}
    return [dict(r) for r in db.execute(sql, params).fetchall()]

def _witchcraft_rows(dev_index: dict[str, dict], rows: list[dict]) -> list[dict]:
    # shape rows same style as /v1/ui/latest_table for just this switch
    out = []
    for r in rows:
        oid = str(r.get("olt_id"))
        meta = dev_index.get(oid) or {}
        devname = meta.get("device_name") or _shortuuid(oid)
        ip      = meta.get("fs_ip") or ""
        port    = meta.get("fs_ifname") or ""
        rx = r.get("dom_rx_dbm"); tx = r.get("dom_tx_dbm")
        # same simple DOM rule you already use
        sev = "n/a"
        try:
            if rx is None:
                sev = "n/a"
            else:
                rxv = float(rx)
                sev = "alarm" if rxv <= -28.0 else ("warning" if rxv <= -24.0 else "normal")
        except Exception:
            sev = "n/a"
        up = bool(r.get("fs_up")) if "fs_up" in r else True
        status = (r.get("fs_status") or "unknown")
        dt_ms = r.get("dt_ms"); dt_ms = None if dt_ms is None else int(dt_ms)
        note = _make_note(error=r.get("error"), up=up, status=status, severity=sev, rx=rx, tx=tx, dt_ms=dt_ms)
        # classify peer and friendly label
        if str(oid).startswith("link-"):
            peer_kind = "switch"
            peer_label = (_LINK_INDEX.get(oid, {}) or {}).get("name") \
                         or _linklabel_from_oid(oid) \
                         or devname
        else:
            peer_kind = "olt"
            peer_label = (_OLT_INDEX.get(oid, {}) or {}).get("name") or devname

        out.append({
            "device": devname,
            "ip": ip,
            "port": port,
            "status": status,
            "up": up,
            "is_link": oid.startswith("link-"),
            "link_label": _linklabel_from_oid(oid) if oid.startswith("link-") else None,
            "peer_kind": peer_kind,  # NEW
            "peer_label": peer_label,  # NEW
            "in_mbps": _mbps(r.get("in_bps")),
            "out_mbps": _mbps(r.get("out_bps")),
            "rx_dbm": rx,
            "tx_dbm": tx,
            "severity": sev,
            "olt_id": oid,
            "note": note,
        })

    return out

def _sum_series_by_bucket(rows: list[dict], val_key: str) -> dict[int, float]:
    # rows: [{ts, olt_id, v}] (agg_5m or raw). We compress by ts (sum across link IDs).
    acc: dict[int, float] = {}
    for r in rows:
        ts = int(r["ts"])
        v  = float(r.get(val_key) or r.get("v") or 0.0)
        acc[ts] = acc.get(ts, 0.0) + v
    return acc

@app.get("/v1/switch/{fs_ip}/snapshot", dependencies=[Depends(require_api_key)])
def switch_snapshot(
    fs_ip: str,
    hours: int = Query(24, ge=1, le=168),
    db: sqlite3.Connection = Depends(_get_db),
):
    devs = _devices_on_switch(db, fs_ip)
    dev_index = {d["olt_id"]: d for d in devs}
    ids = [d["olt_id"] for d in devs]
    latest = _latest_rows_for_olts(db, ids)
    ports = _witchcraft_rows(dev_index, latest)
    series_ids = [p["olt_id"] for p in ports if p.get("olt_id")]

    now = int(time.time())
    last_ts = db.execute("SELECT MAX(ts) AS ts FROM samples_raw").fetchone()["ts"]
    staleness_s = max(0, int(now - int(last_ts or now)))

    # Link IDs on this switch (for charts + KPIs)
    link_ids = [p["olt_id"] for p in ports if p["is_link"]]

    # Throughput KPIs over window (sum of links)
    since = now - hours * 3600
    kpis = {"uplink_avg_in_mbps": 0.0, "uplink_avg_out_mbps": 0.0, "uplink_p95_in_mbps": 0.0, "uplink_p95_out_mbps": 0.0}
    if link_ids:
        ph = ",".join(f":k{i}" for i in range(len(link_ids)))
        params = {"since": since, "until": now, **{f"k{i}": v for i, v in enumerate(link_ids)}}
        rows5 = db.execute(f"""
            SELECT bucket_ts AS ts, olt_id, avg_in_bps, avg_out_bps
            FROM agg_5m
            WHERE bucket_ts >= :since AND bucket_ts < :until AND olt_id IN ({ph})
            ORDER BY bucket_ts ASC
        """, params).fetchall()
        rows5 = [dict(r) for r in rows5]
        sums_in  = _sum_series_by_bucket(rows5, "avg_in_bps")
        sums_out = _sum_series_by_bucket(rows5, "avg_out_bps")
        in_vals  = [v/1_000_000.0 for _, v in sorted(sums_in.items())]
        out_vals = [v/1_000_000.0 for _, v in sorted(sums_out.items())]
        if in_vals:
            kpis["uplink_avg_in_mbps"]  = round(mean(in_vals), 1)
            kpis["uplink_p95_in_mbps"]  = round(_pct95(in_vals), 1)
        if out_vals:
            kpis["uplink_avg_out_mbps"] = round(mean(out_vals), 1)
            kpis["uplink_p95_out_mbps"] = round(_pct95(out_vals), 1)

    # DOM health counts
    dom_alarms   = sum(1 for p in ports if p["severity"] == "alarm")
    dom_warnings = sum(1 for p in ports if p["severity"] == "warning")

    # Current ONU context for OLTs on this switch
    olt_ids_on_switch = [p["olt_id"] for p in ports if not p["is_link"]]
    offline_by_olt: dict[str, int] = {}
    if os.path.exists(STATE_PATH):
        try:
            with open(STATE_PATH, "r", encoding="utf-8") as f:
                st = json.load(f)
            for rec in st.values():
                oid = str(rec.get("olt_id") or "")
                if oid in olt_ids_on_switch and not rec.get("recovered_at"):
                    offline_by_olt[oid] = offline_by_olt.get(oid, 0) + 1
        except Exception:
            pass

    # Action items (pragmatic, not cute)
    actions: list[dict] = []
    def add_action(level: str, text: str, hint: str = ""):
        actions.append({"level": level, "text": text, "hint": hint})

    if kpis["uplink_p95_in_mbps"] > 0 or kpis["uplink_p95_out_mbps"] > 0:
        if kpis["uplink_p95_in_mbps"] > 0.95 * 1000 or kpis["uplink_p95_out_mbps"] > 0.95 * 1000:  # ~1Gb links example
            add_action("critical", "Uplink p95 near line rate", "Consider capacity plan, QoS, or reroute.")
        elif kpis["uplink_p95_in_mbps"] > 0.8 * 1000 or kpis["uplink_p95_out_mbps"] > 0.8 * 1000:
            add_action("warn", "High uplink utilization", "Watch for microbursts, check queue drops.")

    if dom_alarms:
        add_action("critical", f"{dom_alarms} port(s) in DOM alarm", "Clean optics, reseat SFP, inspect plant.")
    if dom_warnings and not dom_alarms:
        add_action("warn", f"{dom_warnings} port(s) in DOM warning", "Check patch cords and bend radius.")

    if sum(offline_by_olt.values()) > 0:
        add_action("warn", f"{sum(offline_by_olt.values())} ONU(s) offline across local OLT(s)",
                   "Open OLT page to triage per-PON clusters.")

    return {
        "fs_ip": fs_ip,
        "now": now,
        "staleness_s": staleness_s,
        "kpis": kpis,
        "dom": {"alarms": dom_alarms, "warnings": dom_warnings},
        "offline_onus": offline_by_olt,
        "ports": ports,
        "link_ids": link_ids,
        "hours": hours,
        "actions": actions,
        "series_ids": series_ids,  # NEW: use this in the 60m/24h/7d charts
        # placeholder: simple “events” for now (we can add flaps/errors later)
        "events": [
            *([{"when": now, "level": "info", "what": f"{len(link_ids)} link(s) monitored on this switch"}]),
            *([{"when": now, "level": "warn", "what": f"{sum(offline_by_olt.values())} ONU(s) offline on attached OLTs"}] if sum(offline_by_olt.values()) else []),
        ],
    }

@app.get("/v1/switch/{fs_ip}/sparklines", dependencies=[Depends(require_api_key)])
def switch_sparklines(
    fs_ip: str,
    minutes: int = Query(60, ge=10, le=12*60),
    db: sqlite3.Connection = Depends(_get_db),
):
    """
    Return 5m-series sparklines for *all* monitored ports on this FS3900, not just link-... IDs.
    """
    now = int(time.time())
    since = now - minutes * 60
    devs = _devices_on_switch(db, fs_ip)           # [{olt_id, device_name, fs_ip, fs_ifname}, ...]
    ids  = [d["olt_id"] for d in devs if d.get("olt_id")]
    out: list[dict] = []
    for d in devs:
        oid = d["olt_id"]
        label = d.get("device_name") or _linklabel_from_oid(oid) or oid
        rows = db.execute("""
            SELECT bucket_ts AS ts, avg_in_bps, avg_out_bps
            FROM agg_5m
            WHERE bucket_ts >= :since AND bucket_ts < :until AND olt_id = :oid
            ORDER BY bucket_ts ASC
        """, {"since": since, "until": now, "oid": oid}).fetchall()
        in_series  = [[int(r["bucket_ts"])*1000, float(r["avg_in_bps"])/1_000_000.0]  for r in rows]
        out_series = [[int(r["bucket_ts"])*1000, float(r["avg_out_bps"])/1_000_000.0] for r in rows]
        out.append({"olt_id": oid, "label": label, "in": in_series, "out": out_series})
    return {"fs_ip": fs_ip, "since_ms": since*1000, "until_ms": now*1000, "series": out}
