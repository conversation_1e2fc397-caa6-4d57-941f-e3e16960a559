# notify.py
from __future__ import annotations

import logging
import time
import urllib.parse
from typing import Iterable
import smtplib
import ssl
from email.message import EmailMessage
import os
import requests
import io
import sqlite3
from typing import List, Tuple, Optional, Dict
from datetime import datetime

try:
    from zoneinfo import ZoneInfo  # py3.9+
except Exception:  # pragma: no cover
    ZoneInfo = None  # type: ignore

from config import settings

log = logging.getLogger("notify")


def normalize_phone(raw: str | None) -> str | None:
    """Normalize to E.164-ish. Return None if empty/unparseable."""
    if not raw:
        return None
    s = "".join(ch for ch in str(raw) if ch.isdigit() or ch == "+").strip()
    if not s:
        return None
    if s.startswith("+"):
        return s
    digits = "".join(ch for ch in s if ch.isdigit())
    if not digits:
        return None
    if len(digits) == 11 and digits.startswith("1"):
        return f"+{digits}"
    if len(digits) == 10:
        return f"+1{digits}"
    return digits


_last_sms_ts: float = 0.0  # simple, process-local pacing


def _parse_vitelity_ok(text: str) -> bool:
    """Vitelity success token inside HTML."""
    try:
        return "x[[ok[[x" in (text or "").lower()
    except Exception:
        return False


def send_sms_raw(phone: str, body: str, *, timeout: float = 10.0) -> bool:
    """
    Send SMS via Vitelity HTTP API (idempotent from caller’s perspective).
    Return True only if provider returns the 'ok' token. Quiet on HTML page noise.
    """
    norm = normalize_phone(phone)
    if not norm:
        log.warning("[SMS] invalid phone: %r", phone)
        return False

    params = {
        "login": settings.VITELITY_USERNAME,
        "pass": settings.VITELITY_PASSWORD,
        "cmd": "sendsms",
        "src": settings.VITELITY_FROM_NUMBER,
        "dst": norm.lstrip("+"),
        "msg": body,
    }
    url = "https://smsout-api.vitelity.net/api.php?" + urllib.parse.urlencode(params)
    log.info("[SMS] %s -> %s", norm, body)
    try:
        resp = requests.get(url, timeout=timeout)
        ok = _parse_vitelity_ok(resp.text)
        if ok:
            log.info("[SMS] accepted :: http=%s", resp.status_code)
        else:
            # provider returns a verbose HTML page; that’s not actionable—keep it at DEBUG
            snippet = (resp.text or "").strip()
            if len(snippet) > 240:
                snippet = snippet[:240] + "..."
            log.debug("[SMS] provider reply (not-ok): http=%s body=%r", resp.status_code, snippet)
        return ok
    except requests.RequestException as e:
        log.warning("[SMS] transport error: %s", e)
        return False


def send_sms_rate_limited(phone: str, body: str, *, min_gap_sec: float = 3.0) -> bool:
    """Enforce a small gap between provider calls."""
    global _last_sms_ts
    now = time.time()
    wait = _last_sms_ts + min_gap_sec - now
    if wait > 0:
        time.sleep(wait)
    ok = send_sms_raw(phone, body)
    _last_sms_ts = time.time()
    return ok

def send_email(recipients, subject: str, body: str, html_body: str | None = None) -> None:
    """
    Send an email via SMTP (Outlook 365 by default).
    - Plain-text 'body' is always included.
    - If 'html_body' is provided, send multipart/alternative with HTML.
    Honors EMAIL_REDIRECT_TO.
    """
    to_list = [str(r).strip() for r in (recipients or []) if r]
    if not to_list:
        log.info("[EMAIL] no recipients for subject=%r", subject)
        return

    try:
        from config import settings  # type: ignore
    except Exception:
        settings = None  # noqa: F841

    host = (getattr(settings, "SMTP_HOST", None) or os.getenv("SMTP_HOST") or "smtp.office365.com")
    port = int(getattr(settings, "SMTP_PORT", None) or os.getenv("SMTP_PORT") or "587")
    user = (getattr(settings, "SMTP_USER", None) or os.getenv("SMTP_USER") or "")
    password = (getattr(settings, "SMTP_PASSWORD", None) or os.getenv("SMTP_PASSWORD") or "")
    from_addr = (getattr(settings, "FROM_EMAIL", None) or os.getenv("FROM_EMAIL") or user)

    redirect = (getattr(settings, "EMAIL_REDIRECT_TO", None) or os.getenv("EMAIL_REDIRECT_TO") or "").strip()
    if redirect:
        to_list = [s.strip() for s in redirect.split(",") if s.strip()]

    if not host or not user or not password or not from_addr:
        log.info("[EMAIL] SMTP not configured; would send to=%s subj=%s", ", ".join(to_list), subject)
        return

    msg = EmailMessage()
    msg["Subject"] = subject
    msg["From"] = from_addr
    msg["To"] = ", ".join(to_list)

    # Always include text; add HTML alternative if provided
    msg.set_content(body)
    if html_body:
        msg.add_alternative(html_body, subtype="html")

    try:
        with smtplib.SMTP(host, port, timeout=20) as s:
            s.ehlo()
            s.starttls(context=ssl.create_default_context())
            s.ehlo()
            s.login(user, password)
            s.send_message(msg)
        log.info("[EMAIL] sent to=%s subj=%s", ", ".join(to_list), subject)
    except Exception as e:
        log.warning("[EMAIL] send failed: %s", e)

# ---------------- Messaging templates (pure string renderers) ----------------

from datetime import datetime, timezone

def _fmt_ts(ts: float|None) -> str:
    if not ts:
        return ""
    # Render in local time if you prefer; keeping UTC clear is fine too.
    return datetime.fromtimestamp(ts, tz=timezone.utc).strftime("%Y-%m-%d %H:%M:%SZ")

def render_customer_outage(ctx: dict) -> tuple[str, str, str]:
    """
    ctx: {
      "customer_name": str|None,
      "street": str|None,
      "window_minutes": int,        # escalation window used (e.g., 5)
      "support_phone": str|None,
    }
    Returns: (email_subject, email_body, sms_text)
    """
    subject = "Heads-up: We’re investigating a service interruption"
    name = ctx.get("customer_name") or "Customer"
    street = f" at {ctx['street']}" if ctx.get("street") else ""
    window = ctx.get("window_minutes") or 5
    support = ctx.get("support_phone") or ""
    body = (
        f"Hi {name},\n\n"
        f"We’re seeing a connectivity issue for your service{street}. "
        f"Our network team is investigating now. No action needed on your end.\n\n"
        f"Important: please don’t reset your router or ONT/ONU unless we ask you to. "
        f"Avoiding restarts helps us diagnose and restore your service faster.\n\n"
        f"We’ll email/text you as soon as this is resolved. "
        f"If you need to talk to us, call {support}.\n\n"
        f"– Eastern Shore Communications NOC"
    )
    sms = "ESC: We’re investigating a service interruption at your address. No action needed. We’ll update you when it’s resolved."
    return subject, body, sms

def render_customer_recovery(ctx: dict) -> tuple[str, str, str]:
    """
    ctx: {
      "customer_name": str|None,
      "street": str|None,
      "support_phone": str|None,
    }
    """
    subject = "Service restored"
    name = ctx.get("customer_name") or "Customer"
    street = f" at {ctx['street']}" if ctx.get("street") else ""
    support = ctx.get("support_phone") or ""
    body = (
        f"Hi {name},\n\n"
        f"Your service{street} is back online.\n\n"
        f"If you still have issues, reboot your router once, then wait a few minutes. "
        f"If problems persist, give us a call at {support}.\n\n"
        f"– Eastern Shore Communications NOC"
    )
    sms = "ESC: Your service is restored. If issues persist, reboot your router once and wait a few minutes."
    return subject, body, sms

def render_staff_outage(ctx: dict) -> tuple[str, str, str]:
    """
    ctx: {
      "region": "WL",
      "region_name": "Wharfs Landing",
      "olt_name": str,
      "site_name": str,
      "affected_count": int,
      "first_seen": float (epoch),
      "fs3900": { "ping_ok": bool|None, "uplink": "up/down/unknown", "port": "xe-0/0/0", "dom": {...}, "traffic": {"in": bps, "out": bps} } | None
    }
    """
    region = ctx.get("region") or ""
    rname  = ctx.get("region_name") or ""
    olt    = ctx.get("olt_name") or ""
    site   = ctx.get("site_name") or ""
    n      = ctx.get("affected_count") or 0
    t0     = _fmt_ts(ctx.get("first_seen"))
    f = ctx.get("fs3900") or {}
    ping = f"ping={'OK' if f.get('ping_ok') else 'FAIL' if f.get('ping_ok') is False else 'unknown'}"
    upl  = f"uplink={f.get('uplink','unknown')}"
    port = f"port={f.get('port','?')}"
    # Keep body concise; details can be expanded if you attach structured diagnostics
    subject = f"[NOC] {region} ({rname}) – Outage on {olt or site} impacting {n}"
    body = (
        f"Region: {region} ({rname})\n"
        f"Site/OLT: {site or olt}\n"
        f"Affected endpoints: {n}\n"
        f"Start: {t0}\n"
        f"FS3900: {ping}, {upl}, {port}\n"
    )
    sms = f"NOC: {region}/{rname} outage on {olt or site}, {n} endpoints. Start {t0}. {ping}, {upl}"
    return subject, body, sms

def render_staff_recovery(ctx: dict) -> tuple[str, str, str]:
    """
    ctx: {
      "region": str, "region_name": str,
      "olt_name": str, "site_name": str,
      "affected_count": int,
      "first_seen": float, "recovered_at": float,
      "fs3900": {...} | None
    }
    """
    region = ctx.get("region") or ""
    rname  = ctx.get("region_name") or ""
    olt    = ctx.get("olt_name") or ""
    site   = ctx.get("site_name") or ""
    n      = ctx.get("affected_count") or 0
    t0     = _fmt_ts(ctx.get("first_seen"))
    t1     = _fmt_ts(ctx.get("recovered_at"))
    subject = f"[NOC] {region} ({rname}) – Recovery on {olt or site} ({n} endpoints)"
    body = (
        f"Region: {region} ({rname})\n"
        f"Site/OLT: {site or olt}\n"
        f"Affected endpoints: {n}\n"
        f"Start: {t0}\n"
        f"Recovery: {t1}\n"
    )
    sms = f"NOC: {region}/{rname} recovery {olt or site}. {n} endpoints. {t1}."
    return subject, body, sms

def render_api_health_down(ctx: dict) -> tuple[str, str, str]:
    base = ctx.get("base_url") or "UISP"
    det  = ctx.get("detail") or ""
    hint = ctx.get("site_hint") or ""
    subject = "[NOC] UISP API unreachable – cluster detection paused"
    body = (
        f"UISP API ({base}) appears unreachable or degraded. Cluster detection and ticket automation are suppressed.\n"
        f"{det}\n"
        f"Nearest site probe: {hint}\n"
        f"FS3900 fallback continues to run.\n"
    )
    sms = "NOC: UISP API unreachable – cluster detection paused. Fallback checks still running."
    return subject, body, sms

def render_api_health_recovered(ctx: dict) -> tuple[str, str, str]:
    base = ctx.get("base_url") or "UISP"
    det  = ctx.get("detail") or ""
    subject = "[NOC] UISP API recovered"
    body = f"UISP API ({base}) recovered.\n{det}\nCluster detection and ticketing re-enabled."
    sms = "NOC: UISP API recovered. Automation re-enabled."
    return subject, body, sms

class MessageBuilder:
    """
    MessageBuilder — daily digest email formatter (no I/O here).
    Produces subject, plaintext body, and a basic HTML version.

    Methods
    -------
    build_daily_digest_email(summary, tz_name) -> (subject, body_text, body_html)
    """

    @staticmethod
    def _fmt_ts(ts: int, tz_name: str) -> str:
        try:
            dt = datetime.fromtimestamp(ts)
            if ZoneInfo:
                dt = dt.astimezone(ZoneInfo(tz_name))
            return dt.strftime("%Y-%m-%d %H:%M")
        except Exception:
            return str(ts)

    @classmethod
    def build_daily_digest_email(cls, *, summary: Dict, tz_name: str = "America/New_York") -> Tuple[str, str, str]:
        outs = summary.get("outages", []) or []
        doms = summary.get("dom_alerts", []) or []
        subject = f"[NOC] Daily report {datetime.now().strftime('%Y-%m-%d')}: {len(outs)} outages, {len(doms)} optic alerts"

        # Plain text body
        lines = []
        lines.append(f"Outages / degraded (last 24h): {len(outs)}")
        for o in outs[:100]:
            first = cls._fmt_ts(o["first_ts"], tz_name)
            last  = cls._fmt_ts(o["last_ts"], tz_name)
            lines.append(f"- {o['name']}  {o['fs_ifname']:<8}  bad={o['bad_samples']}  window={first} → {last}")

        lines.append("")
        lines.append(f"Optical power warnings/alarms (last 24h): {len(doms)}")
        for a in doms[:200]:
            ts = cls._fmt_ts(a["ts"], tz_name)
            rx = a.get("rx_dbm")
            state = a.get("state")
            lim = a.get("limits")
            lines.append(f"- {a['fs_ip']}  {a['fs_ifname']:<8}  rx={rx:.2f} dBm  state={state}  limits={lim}  @ {ts}")

        body_text = "\n".join(lines) if lines else "No issues in the last 24 hours."

        # Simple HTML mirror (tables, nothing fancy)
        html = [
            "<html><body>",
            f"<h2>Daily Network Report — {datetime.now().strftime('%Y-%m-%d')}</h2>",
            f"<p><strong>Outages/degraded (24h):</strong> {len(outs)}</p>",
            "<table border='1' cellpadding='4' cellspacing='0'>",
            "<tr><th>Name</th><th>Interface</th><th>Bad Samples</th><th>Window</th></tr>",
        ]
        for o in outs[:100]:
            first = cls._fmt_ts(o["first_ts"], tz_name)
            last  = cls._fmt_ts(o["last_ts"], tz_name)
            html.append(f"<tr><td>{o['name']}</td><td>{o['fs_ifname']}</td><td>{o['bad_samples']}</td><td>{first} → {last}</td></tr>")
        html += ["</table>", "<br/>",
                 f"<p><strong>Optical power warnings/alarms (24h):</strong> {len(doms)}</p>",
                 "<table border='1' cellpadding='4' cellspacing='0'>",
                 "<tr><th>Switch IP</th><th>Interface</th><th>rx dBm</th><th>State</th><th>Limits</th><th>Time</th></tr>"]
        for a in doms[:200]:
            ts = cls._fmt_ts(a["ts"], tz_name)
            rx = a.get("rx_dbm")
            state = a.get("state")
            lim = a.get("limits")
            html.append(f"<tr><td>{a['fs_ip']}</td><td>{a['fs_ifname']}</td><td>{rx:.2f}</td><td>{state}</td><td>{lim}</td><td>{ts}</td></tr>")
        html += ["</table>", "</body></html>"]
        body_html = "\n".join(html)

        return subject, body_text, body_html


class ChartBuilder:
    """
    ChartBuilder — builds lightweight inline charts as email attachments.
    Produces a small set of PNGs from agg_5m for the last 24h.

    Methods
    -------
    render_olt_charts(db_path, since_ts, limit_olts) -> List[(filename, bytes)]
    """

    @staticmethod
    def _fetch_series(db_path: str, since_ts: int) -> List[Tuple[str, List[Tuple[int, float, float, Optional[float]]]]]:
        """
        Returns: list of (olt_id, [(ts, in_bps, out_bps, avg_dom_rx_dbm), ...])
        """
        con = sqlite3.connect(db_path)
        try:
            cur = con.cursor()
            # Pick top OLTs by activity in window and fetch their series
            olt_rows = cur.execute("""
                SELECT olt_id, COUNT(*) AS n
                FROM agg_5m
                WHERE bucket_ts >= ?
                GROUP BY olt_id
                ORDER BY n DESC
                LIMIT 24
            """, (since_ts,)).fetchall()
            out: List[Tuple[str, List[Tuple[int, float, float, Optional[float]]]]] = []
            for olt_id, _ in olt_rows:
                pts = cur.execute("""
                    SELECT bucket_ts, avg_in_bps, avg_out_bps, avg_dom_rx_dbm
                    FROM agg_5m
                    WHERE olt_id = ? AND bucket_ts >= ?
                    ORDER BY bucket_ts ASC
                """, (olt_id, since_ts)).fetchall()
                out.append((olt_id, [(int(t), float(i or 0), float(o or 0), (None if d is None else float(d))) for t, i, o, d in pts]))
            return out
        finally:
            con.close()

    @classmethod
    def render_olt_charts(cls, *, db_path: str, since_ts: int, limit_olts: int = 12) -> List[Tuple[str, bytes]]:
        """
        Inputs:
          - db_path: SQLite path
          - since_ts: earliest bucket_ts to include
          - limit_olts: max OLTs to chart

        Returns:
          - List of (filename, png_bytes) for attaching to email
        """
        try:
            import matplotlib
            matplotlib.use("Agg")
            import matplotlib.pyplot as plt
        except Exception:
            # Matplotlib not available; quietly skip
            return []

        charts: List[Tuple[str, bytes]] = []
        series = cls._fetch_series(db_path, since_ts)[:limit_olts]

        for olt_id, pts in series:
            if not pts:
                continue
            ts = [p[0] for p in pts]
            in_b = [p[1] for p in pts]
            out_b = [p[2] for p in pts]
            rx = [p[3] if p[3] is not None else float("nan") for p in pts]

            plt.figure(figsize=(6, 3.2))  # single compact chart per OLT
            ax1 = plt.gca()
            ax1.plot(ts, in_b, label="in_bps")
            ax1.plot(ts, out_b, label="out_bps")
            ax1.set_xlabel("time")
            ax1.set_ylabel("bps")
            ax1.tick_params(axis="x", rotation=30)
            ax1.legend(loc="upper left")

            # Secondary axis for DOM RX (dBm)
            try:
                ax2 = ax1.twinx()
                ax2.plot(ts, rx, linestyle="--", label="dom_rx_dbm")
                ax2.set_ylabel("dBm")
            except Exception:
                pass

            plt.title(f"{olt_id} — 24h")
            buf = io.BytesIO()
            plt.tight_layout()
            plt.savefig(buf, format="png")
            plt.close()
            charts.append((f"{olt_id.replace('/', '_')}_24h.png", buf.getvalue()))

        return charts