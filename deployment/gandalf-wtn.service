[Unit]
Description=Gandalf WTN Outage Monitor
Documentation=https://github.com/Eastern-Shore-Communications/gandalf_wtn
After=network.target redis.service
Requires=redis.service

[Service]
Type=simple
User=gandalf-wtn
Group=gandalf-wtn
WorkingDirectory=/opt/gandalf-wtn

# Environment
Environment=PATH=/opt/gandalf-wtn/.venv/bin
EnvironmentFile=/etc/gandalf-wtn/environment

# Logging configuration
Environment=LOG_FILE=/var/log/gandalf-wtn/gandalf.log
Environment=LOG_LEVEL=INFO

# Main process
ExecStart=/opt/gandalf-wtn/.venv/bin/python run_Local_loop.py

# Process management
Restart=always
RestartSec=10
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30

# Security
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/lib/gandalf-wtn /var/log/gandalf-wtn

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=gandalf-wtn

[Install]
WantedBy=multi-user.target
