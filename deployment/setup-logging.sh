#!/bin/bash
# Setup logging for Gandalf WTN on Ubuntu

set -e

echo "Setting up Gandalf WTN logging..."

# Create log directories
sudo mkdir -p /var/log/gandalf-wtn
sudo chown gandalf-wtn:gandalf-wtn /var/log/gandalf-wtn
sudo chmod 755 /var/log/gandalf-wtn

# Install logrotate configuration
sudo cp deployment/logrotate.conf /etc/logrotate.d/gandalf-wtn
sudo chown root:root /etc/logrotate.d/gandalf-wtn
sudo chmod 644 /etc/logrotate.d/gandalf-wtn

# Test logrotate configuration
echo "Testing logrotate configuration..."
sudo logrotate -d /etc/logrotate.d/gandalf-wtn

# Install systemd service
sudo cp deployment/gandalf-wtn.service /etc/systemd/system/
sudo systemctl daemon-reload

echo "Logging setup complete!"
echo ""
echo "Log locations:"
echo "  Application logs: /var/log/gandalf-wtn/gandalf.log"
echo "  SystemD journal:  sudo journalctl -u gandalf-wtn -f"
echo ""
echo "Log management:"
echo "  View logs:        sudo tail -f /var/log/gandalf-wtn/gandalf.log"
echo "  Rotate logs:      sudo logrotate /etc/logrotate.d/gandalf-wtn"
echo "  Service status:   sudo systemctl status gandalf-wtn"
