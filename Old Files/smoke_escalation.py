#!/usr/bin/env python3
"""
Rebuilt smoke_escalation harness based on the new simulated driver.

Adds:
- --print-events : dump captured [STAFF/CUST SMS], [EMAIL], [TICKET] lines in chronological order
- --json-out FILE: export per-tick timeline + events for plotting/postmortems
- scenario: restore_midway — verifies step-down and (if any were opened) ticket closes

Usage examples:
  TEST_MODE=1 python smoke_escalation.py burst_one_olt --ticks 180 --tick-sec 1 --print-events
  TEST_MODE=1 python smoke_escalation.py multi_olt_cut --ticks 240 --tick-sec 1 --json-out out.json
  TEST_MODE=1 python smoke_escalation.py restore_midway --ticks 220 --tick-sec 1 --print-events --json-out restore.json
  TEST_MODE=1 python smoke_escalation.py all

Exit code 0 = pass, non-zero = failed assertions.
"""
from __future__ import annotations

import os
import sys
import json
import argparse
from typing import Dict, List, Iterable, Tuple, Any

# Ensure test-mode chatty logs BEFORE importing tasks
os.environ.setdefault("TEST_MODE", "1")

import tasks  # your app under test (imports config, messaging, escalation, etc.)

# ----------------- simulated clock -----------------
class SimClock:
    def __init__(self, start: float, step: float):
        self.t0 = float(start)
        self.step = float(step)
        self.tick = 0

    @property
    def now(self) -> float:
        return self.t0 + self.tick * self.step

    def advance(self, n: int = 1):
        self.tick += int(n)


def _patch_time(clock: SimClock):
    tasks.time.time = lambda: clock.now  # type: ignore[attr-defined]


# ----------------- scenario generator -----------------
Row = Dict[str, object]

class Scenario:
    def __init__(self, regions: List[str]):
        self.regions = regions
        self.offline_by_tick: Dict[int, Dict[str, List[str]]] = {}  # tick -> region -> [onu_id]
        self.directory: Dict[str, Row] = {}  # onu_id -> row template

    def set_offline(self, tick: int, region: str, onu_ids: Iterable[str]):
        self.offline_by_tick.setdefault(tick, {})[region] = list(onu_ids)

    def ensure_onu(self, onu_id: str, endpoint: str, client_id: int, addr: str,
                   phone: str = "+15555550123", email: str = "<EMAIL>"):
        self.directory[onu_id] = {
            "onu_id": onu_id,
            "endpoint": endpoint,
            "client_id": client_id,
            "client_addr": addr,
            "phone": phone,
            "email": email,
            "sms_enabled": True,
            "email_enabled": True,
        }

    def detector_rows(self, tick: int, region: str) -> List[Row]:
        ons = self.offline_by_tick.get(tick, {}).get(region, [])
        rows: List[Row] = []
        for onu in ons:
            base = self.directory.get(onu, {
                "onu_id": onu,
                "endpoint": onu,
                "client_id": 0,
                "client_addr": "-",
                "phone": "+15555550123",
                "email": "<EMAIL>",
                "sms_enabled": True,
                "email_enabled": True,
            })
            rows.append(dict(base))
        return rows

    def olts_for_region(self, region: str) -> list[str]:
        prefix = region.upper() + "-"
        return sorted({onu.split("-", 1)[0] for onu in self.directory if onu.startswith(prefix)})


def make_scenario(name: str) -> Scenario:
    regions = ["wtn", "wl", "off", "ccp", "wts"]
    sc = Scenario(regions)

    def reg_onus(prefix: str, count: int, start_index: int = 0, addr_city: str = "City") -> List[str]:
        ids = []
        for i in range(start_index, start_index + count):
            onu = f"{prefix}-{i:04d}"
            sc.ensure_onu(onu, endpoint=f"{prefix}-Cust-{i:04d}", client_id=10_000 + i, addr=f"{i} Main St, {addr_city}")
            ids.append(onu)
        return ids

    if name == "burst_one_olt":
        onus = reg_onus("WTN", 120, addr_city="WTN City")
        # 80 ONUs drop 1/sec for ticks 0..79, then hold
        for t in range(0, 80):
            sc.set_offline(t, "wtn", onus[: t + 1])
        for t in range(80, 300):
            sc.set_offline(t, "wtn", onus[:80])

    elif name == "trickle_one_olt":
        onus = reg_onus("WTN", 30, addr_city="WTN City")
        offline: List[str] = []
        # one extra ONU every 180s up to 20 ONUs
        for t in range(0, 3600, 180):
            if len(offline) < 20:
                offline.append(onus[len(offline)])
            for h in range(t, min(t + 180, 3600)):
                sc.set_offline(h, "wtn", list(offline))

    elif name == "multi_olt_cut":
        wtn = reg_onus("WTN", 100, addr_city="WTN City")
        wl  = reg_onus("WL",  100, addr_city="WL City")
        for t in range(0, 60):
            sc.set_offline(t, "wtn", wtn[: min(30, t + 1)])
            sc.set_offline(t, "wl",  wl[: min(35, t + 1)])
        for t in range(60, 240):
            sc.set_offline(t, "wtn", wtn[:30])
            sc.set_offline(t, "wl",  wl[:35])

    elif name == "flap_guard":
        onus = reg_onus("WTN", 1, addr_city="WTN City")
        onu = onus[0]
        sc.set_offline(0, "wtn", [onu])
        sc.set_offline(1, "wtn", [])
        sc.set_offline(2, "wtn", [onu])
        sc.set_offline(3, "wtn", [])
        for t in range(4, 60):
            sc.set_offline(t, "wtn", [])

    elif name == "restore_midway":
        onus = reg_onus("WTN", 120, addr_city="WTN City")
        # ramp to 80 offline by t=79
        for t in range(0, 80):
            sc.set_offline(t, "wtn", onus[: t + 1])
        # hold 80 until t=119
        for t in range(80, 120):
            sc.set_offline(t, "wtn", onus[:80])
        # restore 50% (down to 40) until t=159
        for t in range(120, 160):
            sc.set_offline(t, "wtn", onus[:40])
        # full restore after t=160
        for t in range(160, 260):
            sc.set_offline(t, "wtn", [])
    elif name == "discover_region_olts":
        # Build inventory in multiple regions (no outages needed)
        for p, city in [("WTN", "WTN City"), ("WL", "WL City"), ("CCP", "CCP City"), ("WTS", "WTS City")]:
            reg_onus(p, 3, addr_city=city)
        # no set_offline calls — pure discovery
    else:
        raise SystemExit(f"unknown scenario: {name}")

    return sc


# ----------------- monkeypatch app under test -----------------
class Captures:
    def __init__(self):
        # raw buckets (legacy)
        self.staff_sms: List[Tuple[str, str]] = []   # (number, text)
        self.customer_sms: List[Tuple[str, str]] = []
        self.emails: List[Tuple[List[str], str]] = []  # (to, subject)
        self.ticket_open: List[str] = []
        self.ticket_close: List[str] = []
        # unified event stream
        self.events: List[Dict[str, Any]] = []  # {ts, kind, ...}


def _support_set() -> set:
    return set([s.strip() for s in os.getenv("SUPPORT_SMS", "+15555550001,+15555550002").split(",") if s.strip()])


def patch_app_for_sim(clock: SimClock, scenario: Scenario, caps: Captures):
    # 1) fake time
    _patch_time(clock)

    # 2) fake detector
    def fake_detect_offline_rows(region: str) -> List[Row]:
        return scenario.detector_rows(int(clock.tick), region)
    tasks.detect_offline_rows = fake_detect_offline_rows  # type: ignore

    # 3) fake sends
    def fake_sms_raw(num: str, msg: str):
        kind = "staff_sms" if num in _support_set() else "customer_sms"
        (caps.staff_sms if kind == "staff_sms" else caps.customer_sms).append((num, msg))
        caps.events.append({"ts": clock.now, "kind": kind, "number": num, "text": msg})
    def fake_email(to: List[str] | tuple | list, subject: str, body: str):
        lst = list(to)
        caps.emails.append((lst, subject))
        caps.events.append({"ts": clock.now, "kind": "email", "to": lst, "subject": subject})
    tasks.send_sms_raw = fake_sms_raw    # type: ignore
    tasks.send_email = fake_email        # type: ignore

    # 4) fake tickets
    def _open(**kw):
        key = kw.get("incident_key", "")
        caps.ticket_open.append(key)
        caps.events.append({"ts": clock.now, "kind": "ticket_open", "incident_key": key})
    def _close(**kw):
        key = kw.get("incident_key", "")
        caps.ticket_close.append(key)
        caps.events.append({"ts": clock.now, "kind": "ticket_close", "incident_key": key})
    tasks.tickets.open_minor_ticket = _open  # type: ignore
    tasks.tickets.close_minor_ticket = _close  # type: ignore

    # 5) deterministic ONU->OLT mapping by prefix (WTN-xxxx -> WTN)
    tasks._olt_id_for_onu = lambda onu_id: onu_id.split("-", 1)[0] if "-" in onu_id else "unknown"  # type: ignore

    # 5b) fake OLT discovery (inventory-driven) + friendly-name passthrough
    def fake_discover_region_olts(region: str) -> list[str]:
        return scenario.olts_for_region(region)
    tasks.discover_region_olts = fake_discover_region_olts  # type: ignore
    tasks.name_for_olt = lambda olt_id: olt_id  # type: ignore


    # 6) ensure we have staff numbers
    os.environ.setdefault("SUPPORT_SMS", "+15555550001,+15555550002")

    # 7) tune config for faster tests (reduce stability requirements)
    tasks.CFG.T_STABLE = 30      # seconds to step-down to NORMAL
    tasks.CFG.T_FLOOR = 15       # seconds below tier floor to ratchet down
    tasks.CFG.TICK_DEBOUNCE = 2  # fewer ticks before escalation beyond OBSERVED


# ----------------- runner -----------------
def snapshot_offline(scenario: Scenario, tick: int) -> Dict[str, int]:
    d: Dict[str, int] = {}
    for r in scenario.regions:
        d[r] = len(scenario.detector_rows(tick, r))
    return d


def run_ticks(clock: SimClock, scenario: Scenario, ticks: int) -> List[Dict[str, str]]:
    """Run poll_and_notify() for N ticks, return per-tick OLT state maps."""
    states_per_tick: List[Dict[str, str]] = []
    for _ in range(ticks):
        report = tasks.poll_and_notify.apply().get() if hasattr(tasks.poll_and_notify, "apply") else tasks.poll_and_notify()
        states = report.get("olts") if isinstance(report, dict) else {}
        states_per_tick.append(states)
        clock.advance(1)
    return states_per_tick


# ----------------- assertions per scenario -----------------
SEV_ORDER = {"NORMAL":0, "OBSERVED":1, "MINOR":2, "MODERATE":3, "CRITICAL":4}

def assert_burst_one_olt(states_per_tick: List[Dict[str, str]], caps: Captures) -> None:
    ever = [t.get("WTN") for t in states_per_tick]
    assert any(s == "CRITICAL" for s in ever), "WTN never reached CRITICAL"
    assert any("CRITICAL on OLT WTN" in m for (_n, m) in caps.staff_sms), "No staff CRITICAL SMS for WTN"


def assert_trickle_one_olt(states_per_tick: List[Dict[str, str]], caps: Captures) -> None:
    ever = [t.get("WTN") for t in states_per_tick]
    max_sev = max((SEV_ORDER.get(s or "NORMAL", 0) for s in ever), default=0)
    assert max_sev <= SEV_ORDER["MINOR"], f"Trickle exceeded MINOR: saw {set(ever)}"
    assert not any("outage :: OLTs:" in m for (_n, m) in caps.staff_sms), "Unexpected cluster SMS during trickle"


def assert_multi_olt_cut(states_per_tick: List[Dict[str, str]], caps: Captures) -> None:
    wtn_max = max((SEV_ORDER.get(t.get("WTN","NORMAL"),0) for t in states_per_tick), default=0)
    wl_max  = max((SEV_ORDER.get(t.get("WL","NORMAL"),0)  for t in states_per_tick), default=0)
    assert wtn_max >= SEV_ORDER["MODERATE"], f"WTN did not reach MODERATE (max={wtn_max})"
    assert wl_max  >= SEV_ORDER["MODERATE"], f"WL did not reach MODERATE (max={wl_max})"
    assert any("WATCH outage" in m or "CRITICAL outage" in m for (_n, m) in caps.staff_sms), "No cluster SMS detected"
    assert len(caps.ticket_open) == 0, f"Unexpected tickets during regional event: {caps.ticket_open}"


def assert_flap_guard(states_per_tick: List[Dict[str, str]], caps: Captures) -> None:
    ever = [t.get("WTN") for t in states_per_tick]
    max_sev = max((SEV_ORDER.get(s or "NORMAL", 0) for s in ever), default=0)
    assert max_sev <= SEV_ORDER["OBSERVED"], f"Flap exceeded OBSERVED: {set(ever)}"
    assert not any("MODERATE on OLT WTN" in m or "CRITICAL on OLT WTN" in m for (_n, m) in caps.staff_sms), "Unexpected high-sev staff SMS during flap"


def assert_restore_midway(states_per_tick: List[Dict[str, str]], caps: Captures) -> None:
    ever = [t.get("WTN") for t in states_per_tick]
    assert any(s == "CRITICAL" for s in ever), "WTN never reached CRITICAL"
    # step-down path observed
    assert any(s == "MODERATE" for s in ever if s), "No MODERATE during recovery"
    assert any(s == "MINOR" for s in ever if s), "No MINOR during recovery"
    assert ever[-1] == "NORMAL", f"Final state not NORMAL: {ever[-1]}"
    # Ticket hygiene: if any opens, ensure closes >= opens
    if caps.ticket_open:
        assert len(caps.ticket_close) >= len(caps.ticket_open), f"Ticket closes ({len(caps.ticket_close)}) < opens ({len(caps.ticket_open)})"

def assert_discover_region_olts(_states_per_tick, _caps):
    expected = {
        "wtn": {"WTN"},
        "wl":  {"WL"},
        "ccp": {"CCP"},
        "wts": {"WTS"},
        "off": set(),
    }
    for region, exp in expected.items():
        got = set(tasks.discover_region_olts(region))
        assert got == exp, f"discover_region_olts({region}) -> {got} (expected {exp})"


# ----------------- CLI + printing/json -----------------
SCENARIOS = {
    "burst_one_olt": (assert_burst_one_olt, 180, 1.0),
    "trickle_one_olt": (assert_trickle_one_olt, 3600, 1.0),
    "multi_olt_cut": (assert_multi_olt_cut, 240, 1.0),
    "flap_guard": (assert_flap_guard, 60, 1.0),
    "restore_midway": (assert_restore_midway, 220, 1.0),
    "discover_region_olts": (assert_discover_region_olts, 5, 1.0),
}


def _print_events(caps: Captures):
    events = sorted(caps.events, key=lambda e: e["ts"])
    for e in events:
        kind = e["kind"]
        ts = int(e["ts"]) if isinstance(e["ts"], (int, float)) else e["ts"]
        if kind == "staff_sms":
            print(f"[STAFF SMS] t={ts} to {e['number']}: {e['text']}")
        elif kind == "customer_sms":
            print(f"[CUST  SMS] t={ts} to {e['number']}: {e['text']}")
        elif kind == "email":
            print(f"[EMAIL] t={ts} to {','.join(e['to'])} subj='{e['subject']}'")
        elif kind == "ticket_open":
            print(f"[TICKET OPEN] t={ts} key={e['incident_key']}")
        elif kind == "ticket_close":
            print(f"[TICKET CLOSE] t={ts} key={e['incident_key']}")


def _build_timeline(clock: SimClock, scenario: Scenario, states_per_tick: List[Dict[str, str]]):
    timeline: List[Dict[str, Any]] = []
    # Reconstruct per-tick ts based on clock params: t0 + i*step
    for i, states in enumerate(states_per_tick):
        ts = clock.t0 + i * clock.step
        offline = {r: len(scenario.detector_rows(i, r)) for r in scenario.regions}
        timeline.append({"tick": i, "ts": ts, "olts": states, "offline_counts": offline})
    return timeline


def main():
    ap = argparse.ArgumentParser(description="Rebuilt smoke escalation tests using simulated harness")
    ap.add_argument("scenario", nargs="?", default="all", choices=["all"] + list(SCENARIOS.keys()))
    ap.add_argument("--ticks", type=int, default=None, help="override ticks for scenario")
    ap.add_argument("--tick-sec", type=float, default=None, help="override seconds per tick")
    ap.add_argument("--start-epoch", type=float, default=1_700_000_000.0)
    ap.add_argument("--print-events", action="store_true", help="print captured SMS/EMAIL/TICKET events")
    ap.add_argument("--json-out", type=str, default=None, help="path to write timeline+events JSON")
    args = ap.parse_args()

    failures: List[str] = []

    def run_one(name: str):
        assert name in SCENARIOS
        asserter, default_ticks, default_step = SCENARIOS[name]
        ticks = args.ticks or default_ticks
        step = args.tick_sec or default_step
        clock = SimClock(args.start_epoch, step)
        scenario = make_scenario(name)
        caps = Captures()
        patch_app_for_sim(clock, scenario, caps)
        states = run_ticks(clock, scenario, ticks)
        try:
            asserter(states, caps)
            print(f"[OK] {name}")
        except AssertionError as e:
            print(f"[FAIL] {name}: {e}")
            failures.append(f"{name}: {e}")
        # Optional output
        if args.print_events:
            _print_events(caps)
        if args.json_out:
            data = {
                "scenario": name,
                "ticks": ticks,
                "tick_sec": step,
                "start_epoch": clock.t0,
                "timeline": _build_timeline(clock, scenario, states),
                "events": caps.events,
            }
            with open(args.json_out, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"[WROTE] {args.json_out}")

    if args.scenario == "all":
        for name in SCENARIOS.keys():
            run_one(name)
    else:
        run_one(args.scenario)

    if failures:
        sys.exit(1)
    print("All tests passed.")


if __name__ == "__main__":
    main()
