# detector.py
from __future__ import annotations
from collections import defaultdict
from typing import Any, Iterable, List, Dict
from collections import Counter
from config import settings
from uisp_client import UispClient, UispError
import logging

log = logging.getLogger("detector")

# --- tiny utils --------------------------------------------------------------

def _as_items(obj: Any) -> list:
    if obj is None:
        return []
    if isinstance(obj, list):
        return obj
    if isinstance(obj, dict):
        if isinstance(obj.get("items"), list):
            return obj["items"]
        if isinstance(obj.get("data"), list):
            return obj["data"]
    return [obj]


def _normalize_phone_e164(raw: str) -> str:
    s = "".join(ch for ch in (raw or "") if ch.isdigit())
    if len(s) == 10:
        return f"+1{s}"
    if len(s) == 11 and s.startswith("1"):
        return f"+{s}"
    return (raw or "").strip()


def _is_onu_record(rec: dict) -> bool:
    ident = rec.get("identification") or {}
    t = ident.get("type")
    if isinstance(t, str) and t.lower() == "onu":
        return True
    model = (ident.get("model") or (rec.get("overview") or {}).get("model") or "").lower()
    return ("onu" in model) or ("ufiber" in model) or ("uf-" in model)


# --- data acquisition helpers ------------------------------------------------

def _get_offline_endpoints_for_site(client: UispClient, site_id: str) -> list[dict]:
    """
    Pull one site and select endpoints with:
      - status == 'disconnected'
      - NOT suspended
    """
    site = client.get_site_with_ucrm_details(site_id)
    desc = site.get("description") or {}
    endpoints = desc.get("endpoints") or []
    out = []
    for ep in endpoints:
        if not isinstance(ep, dict):
            continue
        status = (ep.get("status") or "").lower()
        suspended = bool(ep.get("suspended"))
        if status == "disconnected" and not suspended:
            out.append(ep)
    return out


def _collect_onus_for_region(client: UispClient, site_id: str, olt_ids: Iterable[str]) -> list[dict]:
    """
    Try ONUs via parentId (OLT). If unsupported (404), fall back to /devices?siteId=<site>
    and filter by ONU type.
    """
    all_onus: list[dict] = []
    for olt in olt_ids or []:
        try:
            data = client.get_onus_by_parent(olt)
        except UispError as e:
            if "404" in str(e):  # many controllers 404 this filter; skip politely
                continue
            raise
        all_onus.extend(_as_items(data))

    if all_onus:
        return all_onus

    devs = _as_items(client.get_devices_by_site(site_id))
    return [d for d in devs if isinstance(d, dict) and _is_onu_record(d)]


def _map_endpoint_to_onus(all_onus: list[dict]) -> dict[str, list[str]]:
    """
    In your controller, ONU.identification.site.id == endpoint.id
    Build: endpoint_id -> [onu_id, ...]
    """
    by_ep: dict[str, list[str]] = defaultdict(list)
    for onu in all_onus:
        if not isinstance(onu, dict):
            continue
        ident = onu.get("identification") or {}
        ep_id = ((ident.get("site") or {}).get("id")) or None
        oid = ident.get("id") or onu.get("id")
        if ep_id and oid:
            by_ep[str(ep_id)].append(str(oid))
    return by_ep


# --- public API --------------------------------------------------------------

def _safe_get(d: dict, *path: Tuple[str, ...], default=None):
    """Try a few alternative paths in dict d, return first that resolves."""
    for p in path:
        cur = d
        ok = True
        for k in p:
            if isinstance(cur, dict) and k in cur:
                cur = cur[k]
            else:
                ok = False
                break
        if ok:
            return cur
    return default


def detect_offline_rows(region_key: str) -> List[Dict]:
    """
    Build a list of per-endpoint OFFLINE ONU rows for a region using ONLY UispClient.

    Row schema:
      {
        "region": str,
        "endpoint": str,
        "endpoint_site_id": str,
        "client_id": int | None,
        "address": str,
        "address_fields": {"street1": str, "city": str, "zip": str},
        "onu_id": str,
      }
    """
    # --- Init client from settings ---
    client = UispClient(
        settings.NSM_BASE_URL,
        settings.NSM_TOKEN,
        crm_base=settings.CRM_BASE_URL,
        crm_token=settings.CRM_TOKEN,
        timeout=15.0,
    )

    # --- Region config ---
    region = settings.LOCATIONS.get(region_key, {})
    site_id: str = region.get("site", "")
    olt_ids: List[str] = region.get("olts", []) or []
    if not site_id:
        log.warning("detector: no site configured for region=%s", region_key)
        return []

    # --- Load site & find offline endpoints (disconnected & not suspended) ---
    site: dict = {}
    # Support either get_site or older get_site_summary
    if hasattr(client, "get_site"):
        site = client.nsm_get_site(site_id) or {}
    else:
        site = client.nsm_get_site(site_id) or {}

    eps = _safe_get(site, ("description", "endpoints"), default=[]) or []
    offline_eps: List[dict] = []
    for ep in eps:
        # ep schema: {id,name,status,type,parentId,suspended,...}
        status = (ep.get("status") or "").lower()
        suspended = bool(ep.get("suspended"))
        if status == "disconnected" and not suspended:
            offline_eps.append(ep)

    if not offline_eps:
        # No offline endpoints in this region
        return []

    # --- Build endpoint_site_id -> [onu_id] map by scanning ONUs under each OLT ---
    ep_to_onus: Dict[str, List[str]] = {}
    for olt_id in olt_ids:
        try:
            onus = client.get_onus_by_parent(olt_id) or []
        except Exception as e:
            log.warning("detector: get_onus_by_parent(%s) failed: %s", olt_id, e)
            continue

        for o in onus:
            # We only care ONUs, but API path already returns ONUs
            oid = _safe_get(o, ("identification", "id"), ("id",), default="")
            ep_site_id = _safe_get(
                o,
                ("identification", "site", "id"),
                ("site", "id"),
                default=""
            )
            if not oid or not ep_site_id:
                continue
            ep_to_onus.setdefault(ep_site_id, []).append(str(oid))

    rows: List[Dict] = []

    # --- For each offline endpoint, resolve ONU(s) + CRM client + address ---
    for ep in offline_eps:
        ep_site_id = str(ep.get("id", "")).strip()
        ep_name = (ep.get("name") or "").strip()
        if not ep_site_id:
            continue

        onu_ids = list({*(ep_to_onus.get(ep_site_id) or [])})  # unique
        if not onu_ids:
            # No ONU recorded under this endpoint (yet); skip
            continue

        for oid in onu_ids:
            # 1) Try device->site->ucrm path first
            client_id, dev_ep_site, dev_ep_name = (None, None, None)
            try:
                client_id, dev_ep_site, dev_ep_name = client.client_id_from_device(oid)
            except Exception:
                client_id, dev_ep_site, dev_ep_name = (None, None, None)

            resolved_site_id = dev_ep_site or ep_site_id
            resolved_ep_name = (dev_ep_name or ep_name or "").strip()

            # 2) Fallback: derive from site.ucrm
            if client_id is None and resolved_site_id:
                try:
                    client_id = client.get_endpoint_crm_client_id(
                        resolved_site_id, endpoint_name=resolved_ep_name
                    )
                except Exception:
                    client_id = None

            # 3) Staff-facing address from CRM
            address = ""
            address_fields = {"street1": "", "city": "", "zip": ""}
            if client_id is not None:
                try:
                    c = client.crm_get_client(client_id) or {}
                    street1 = (c.get("street1") or c.get("invoiceStreet1") or "").strip()
                    city = (c.get("city") or c.get("invoiceCity") or "").strip()
                    zip_code = (c.get("zipCode") or c.get("invoiceZipCode") or "").strip()
                    address = ", ".join([s for s in (street1, city, zip_code) if s])
                    address_fields = {"street1": street1, "city": city, "zip": zip_code}
                except Exception:
                    pass

            # 4) Log for staff (client_id + address included)
            log.info(
                "detector: offline detail: %s(client_id=%s addr=%s) onu=%s",
                resolved_ep_name,
                str(client_id) if client_id is not None else "None",
                address or "-",
                oid,
            )

            # 5) Emit row for notify/ticket layers
            rows.append({
                "region": region_key,
                "endpoint": resolved_ep_name,
                "endpoint_site_id": resolved_site_id,
                "client_id": client_id,
                "address": address,
                "address_fields": address_fields,
                "onu_id": oid,
            })

    return rows



def rows_to_loglist(rows: list[dict]) -> str:
    return ", ".join(f'{r["endpoint"]}({r["onu_id"]})' for r in rows)
