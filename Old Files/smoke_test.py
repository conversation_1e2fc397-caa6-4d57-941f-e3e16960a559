#!/usr/bin/env python3
from __future__ import annotations

import argparse
from datetime import datetime, UTC
from typing import Optional
import json
import re

from config import Settings
from uisp_client import UispClient
from notify import send_sms_raw, send_email


def _build_client(settings: Settings) -> UispClient:
    return UispClient(
        base_url=settings.NSM_BASE_URL,   # NSM controller API (e.g., .../nms/api/v2.1)
        token=settings.NSM_TOKEN,
        crm_base=settings.CRM_BASE_URL,   # CRM API (e.g., .../api/v1.0)
        crm_token=settings.CRM_TOKEN,
        timeout=15.0,
    )

def _upper(s: str | None) -> str:
    return (s or "").upper()

def _maybe(obj, *path, default=None):
    cur = obj
    for p in path:
        if isinstance(cur, dict) and p in cur:
            cur = cur[p]
        else:
            return default
    return cur

def _flatten_strs(obj):
    """Yield stringified leaves from any nested structure."""
    if isinstance(obj, dict):
        for v in obj.values():
            yield from _flatten_strs(v)
    elif isinstance(obj, (list, tuple, set)):
        for v in obj:
            yield from _flatten_strs(v)
    else:
        try:
            s = str(obj).strip()
            if s:
                yield s
        except Exception:
            return

def infer_region_key(settings: Settings, *, olt_id: str, olt_name: str, site: dict) -> str:
    """
    Best-effort region resolver:
    1) Try settings.LOCATIONS (if present)
    2) Heuristic on OLT/Site names: WTN, WL, CCP, WTS, OFF
    Returns UPPERCASE region code or 'UNKNOWN'.
    """
    # 1) Config-driven (very forgiving)
    locs = getattr(settings, "LOCATIONS", None)
    site_id = str(_maybe(site, "id", default="") or _maybe(site, "identification", "id", default=""))
    site_name = _maybe(site, "identification", "name", default="") or _maybe(site, "name", default="")
    U_olt_name = _upper(olt_name)
    U_site_name = _upper(site_name)
    U_olt_id = str(olt_id)

    if isinstance(locs, dict):
        for rkey, blob in locs.items():
            R = _upper(str(rkey))
            # obvious textual match
            if R and (R in U_olt_name or R in U_site_name):
                return R
            # membership match vs any strings in blob
            flat = set(_flatten_strs(blob))
            if U_olt_id in flat or olt_name in flat or site_id in flat or site_name in flat:
                return R

    # 2) Heuristics
    for code in ("WTN", "WL", "CCP", "WTS", "OFF"):
        if code in U_olt_name or code in U_site_name:
            return code

    # last-ditch: short token inside olt name like "-WL-" / "-WTN-"
    m = re.search(r"-(WTN|WL|CCP|WTS|OFF)(?:-|$)", U_olt_name)
    if m:
        return m.group(1)

    return "UNKNOWN"

def _deep_get(d, *path, default=None):
    cur = d
    for p in path:
        if isinstance(cur, dict) and p in cur:
            cur = cur[p]
        else:
            return default
    return cur

def resolve_olt_for_onu(client: UispClient, onu_id: str) -> tuple[str, str, dict]:
    """
    Return (olt_id, olt_name, olt_device_obj) for a given ONU.
    Checks attributes.parentId, identification.parent.id, overview.parentId.
    """
    try:
        d = client.nsm_get_device(onu_id) or {}
    except Exception:
        return ("unknown", "UNKNOWN OLT", {})

    olt_id = (
        _deep_get(d, "attributes", "parentId")
        or _deep_get(d, "identification", "parent", "id")
        or _deep_get(d, "overview", "parentId")
    )
    if not olt_id:
        return ("unknown", "UNKNOWN OLT", {})

    try:
        od = client.nsm_get_device(str(olt_id)) or {}
        name = _deep_get(od, "identification", "name") or str(olt_id)
        return (str(olt_id), str(name), od)
    except Exception:
        return (str(olt_id), str(olt_id), {})

def _site_id_for_onu(client: UispClient, onu_id: str) -> Optional[str]:
    """Find the NSM site id linked to this ONU device, trying common locations."""
    try:
        d = client.nsm_get_device(onu_id) or {}
    except Exception:
        return None
    return (
        _deep_get(d, "identification", "site", "id")
        or _deep_get(d, "identification", "siteId")
        or _deep_get(d, "overview", "siteId")
        or _deep_get(d, "overview", "site", "id")
        or _deep_get(d, "site", "id")
    )

def _crm_client_id_for_site(site_obj: dict) -> Optional[int]:
    """
    Resolve CRM clientId from an NSM site object.
    We try several known schema variants seen in UISP/UCRM.
    """
    candidates = [
        _deep_get(site_obj, "ucrm", "client", "id"),
        _deep_get(site_obj, "description", "ucrmId"),
        _deep_get(site_obj, "identification", "ucrmId"),
        site_obj.get("ucrmId"),
    ]
    for cid in candidates:
        if cid is None:
            continue
        try:
            return int(cid)
        except Exception:
            # sometimes stringy junk; keep trying
            pass
    return None

def _first_nonempty(*vals):
    for v in vals:
        if isinstance(v, str) and v.strip():
            return v.strip()
    return None

def _extract_contact_fields(crm_client: dict) -> tuple[Optional[str], Optional[str]]:
    """
    Return (email, phone). Looks at top-level and contacts[].
    """
    email = _first_nonempty(crm_client.get("email"))
    phone = _first_nonempty(
        crm_client.get("phone"),
        crm_client.get("phoneNumber"),
    )

    contacts = crm_client.get("contacts") or []
    if isinstance(contacts, list):
        for c in contacts:
            if not email:
                email = _first_nonempty(c.get("email"))
            if not phone:
                phone = _first_nonempty(c.get("phone"), c.get("phoneNumber"))
            if email and phone:
                break
    return email, phone

def _search_prefs(crm_client: dict) -> dict:
    """
    Try hard to find custom attributes sms_notify/email_notify anywhere in the client blob.
    Returns dict with keys {'sms_notify': 'yes/no/…', 'email_notify': 'yes/no/…'} if found.
    """
    wanted = {"sms_notify", "email_notify"}
    found: dict[str, str] = {}

    def rec(node):
        if isinstance(node, dict):
            # direct keys
            for k, v in node.items():
                kl = str(k).lower()
                if kl in wanted and kl not in found and isinstance(v, (str, int, bool)):
                    found[kl] = str(v)
            # key/name → value style
            key_name = str(node.get("key") or node.get("name") or node.get("code") or node.get("label") or "").lower()
            if key_name in wanted and key_name not in found:
                val = node.get("value") or node.get("val") or node.get("stringValue")
                if val is not None:
                    found[key_name] = str(val)
            for v in node.values():
                rec(v)
        elif isinstance(node, list):
            for it in node:
                rec(it)

    rec(crm_client)
    return found

def resolve_complete_info(client: UispClient, onu_id: str) -> dict:
    """
    Build a comprehensive record for --complete.
    """
    # ONU device
    dev = client.nsm_get_device(onu_id) or {}
    onu_name = _maybe(dev, "identification", "name", default="") or dev.get("name") or "-"

    # OLT info
    olt_id, olt_name, _ = resolve_olt_for_onu(client, onu_id)

    # Site → CRM
    site_id = (
        _maybe(dev, "identification", "site", "id")
        or _maybe(dev, "identification", "siteId")
        or _maybe(dev, "overview", "siteId")
        or _maybe(dev, "overview", "site", "id")
        or _maybe(dev, "site", "id")
    )
    site = client.nsm_get_site(site_id) if site_id else {}

    # REGION
    try:
        # Settings is available where we build the client; reuse env
        from config import Settings as _S
        region = infer_region_key(_S(), olt_id=str(olt_id), olt_name=str(olt_name), site=site)
    except Exception:
        region = "UNKNOWN"

    # CRM
    crm_id = None
    ucrm = (site or {}).get("ucrm") or {}
    ucrm_client = (ucrm.get("client") or {})
    if isinstance(ucrm_client, dict) and ucrm_client.get("id") is not None:
        try:
            crm_id = int(ucrm_client["id"])
        except Exception:
            pass
    crm = client.crm_get_client(crm_id) if crm_id else {}

    # Client name / address
    def _first(*vals):
        for v in vals:
            if isinstance(v, str) and v.strip():
                return v.strip()
        return None

    client_name = _first(
        crm.get("name"),
        " ".join(x for x in [crm.get("firstName"), crm.get("lastName")] if x),
    ) or "-"

    client_street = _first(crm.get("street1"), crm.get("street"))
    client_city = _first(crm.get("city"))
    client_zip = _first(crm.get("zipCode"), crm.get("zip"), crm.get("postalCode"))

    # Contacts
    email = _first(crm.get("email"))
    phone = _first(crm.get("phone"), crm.get("phoneNumber"))
    for c in crm.get("contacts") or []:
        if not email:
            email = _first(c.get("email"))
        if not phone:
            phone = _first(c.get("phone"), c.get("phoneNumber"))
        if email and phone:
            break

    # Prefs (sms_notify/email_notify), look everywhere
    prefs = {}
    def walk(n):
        if isinstance(n, dict):
            for k, v in n.items():
                kl = str(k).lower()
                if kl in ("sms_notify", "email_notify") and kl not in prefs and isinstance(v, (str, int, bool)):
                    prefs[kl] = str(v)
            key_name = str(n.get("key") or n.get("name") or n.get("code") or n.get("label") or "").lower()
            if key_name in ("sms_notify", "email_notify") and key_name not in prefs:
                val = n.get("value") or n.get("val") or n.get("stringValue")
                if val is not None:
                    prefs[key_name] = str(val)
            for v in n.values():
                walk(v)
        elif isinstance(n, (list, tuple)):
            for it in n:
                walk(it)
    walk(crm)

    return {
        "REGION": region,                         # <-- NEW
        "ONU_ID": onu_id,
        "ONU_NAME": onu_name,
        "OLT_ID": olt_id,
        "OLT_NAME": olt_name,
        "CLIENT_ID": crm_id if crm_id is not None else "-",
        "CLIENT_NAME": client_name,
        "CLIENT_STREET": client_street or "-",
        "CLIENT_CITY": client_city or "-",
        "CLIENT_ZIP": client_zip or "-",
        "CLIENT_EMAIL": email or "-",
        "CLIENT_PHONE": phone or "-",
        "PREF_SMS_NOTIFY": str(prefs.get("sms_notify", "unknown")).lower(),
        "PREF_EMAIL_NOTIFY": str(prefs.get("email_notify", "unknown")).lower(),
    }


def test_mapping(client: UispClient, device_id: str) -> Optional[dict]:
    """
    NSM device -> NSM site -> UCRM linkage -> CRM client + address.
    Returns: {device_id, site_id, crm_client_id, name, address}
    """
    dev = client.nsm_get_device(device_id) or {}
    site = (dev.get("identification") or {}).get("site") or {}
    site_id = site.get("id")
    if not site_id:
        raise RuntimeError("device.identification.site.id missing")

    sdetail = client.nsm_get_site(site_id) or {}
    ucrm = sdetail.get("ucrm") or {}
    ucrm_client = (ucrm.get("client") or {})
    crm_id = ucrm_client.get("id")
    name = ucrm_client.get("name") or site.get("name") or "(unknown)"

    if not crm_id:
        raise RuntimeError("site.ucrm.client.id missing (device not linked to CRM client?)")

    c = client.crm_get_client(int(crm_id)) or {}
    street = (c.get("street1") or "").strip()
    city = (c.get("city") or "").strip()
    zipc = (c.get("zipCode") or "").strip()
    address = ", ".join([x for x in (street, city, zipc) if x])

    return {
        "device_id": device_id,
        "site_id": site_id,
        "crm_client_id": int(crm_id),
        "name": name,
        "address": address or "-",
    }


def test_ticket_create_close(client: UispClient, client_id: int) -> bool:
    print("\n=== SMOKE: CRM ticket create + close ===")
    try:
        now = datetime.now(UTC).strftime("%Y-%m-%d %H:%M:%S UTC")

        payload = {
            "subject": f"ESVC smoke test @ {now}",
            "clientId": int(client_id),
            "status": 0,
            "public": False,
            "activity": [
                {
                    "userId": None,           # comment as system/API
                    "public": False,
                    "comment": {
                        "body": f"Automated smoke test ticket opened at {now}"
                    }
                }
            ]
        }
        created = client.crm_create_ticket(payload) or {}
        tid = created.get("id")
        if not tid:
            raise RuntimeError(f"no ticket id in response: {created!r}")
        print(f"[OK] ticket created id={tid}")

        upd = client.crm_update_ticket(tid, {
            "status": 2,  # Closed
            "activity": [
                {
                    "userId": None,
                    "public": False,
                    "comment": {
                        "body": f"Automated smoke test ticket closed at {now}"
                    }
                }
            ]
        }) or {}
        _ = upd.get("id")  # ensure no error
        print(f"[OK] ticket closed id={tid}")
        return True
    except Exception as e:
        print(f"[ERR] ticket create/close failed: {e}")
        return False


def test_sms(to_number: str, name: str, address: str, device_id: str) -> bool:
    print("\n=== SMOKE: SMS send ===")
    if not to_number:
        print("[SKIP] no SMS recipient provided")
        return False
    try:
        msg = f"[ESVC] Smoke: outage detected for {name or '-'} at {address or '-'} (device {device_id})."
        # send_sms_raw logs provider status internally; here we just call it
        send_sms_raw(to_number, msg)
        print(f"[OK] SMS attempted to {to_number}")
        return True
    except Exception as e:
        print(f"[ERR] SMS failed: {e}")
        return False


def test_email(to_addr: str, name: str, address: str, device_id: str) -> bool:
    print("\n=== SMOKE: Email send ===")
    if not to_addr:
        print("[SKIP] no email recipient provided")
        return False
    try:
        subject = "ESVC smoke test"
        body = (
            "This is a smoke-test email.\n\n"
            f"Device: {device_id}\n"
            f"Client: {name or '-'}\n"
            f"Address: {address or '-'}\n"
            f"Time: {datetime.now(UTC).strftime('%Y-%m-%d %H:%M:%S UTC')}\n"
        )
        send_email([to_addr], subject, body)
        print(f"[OK] email attempted to {to_addr}")
        return True
    except Exception as e:
        print(f"[ERR] email failed: {e}")
        return False


def main():
    ap = argparse.ArgumentParser(description="ESVC Smoke Test (NSM/CRM + SMS/Email/Tickets)")
    ap.add_argument("device_id", nargs="?", help="NSM device GUID to map (ONU or device)")
    ap.add_argument("--ticket-client", type=int, help="CRM clientId for ticket test (uses mapping if omitted)")
    ap.add_argument("--ticket-only", type=int, metavar="CLIENT_ID", help="Only run ticket test for this clientId")
    ap.add_argument("--sms-to", help="E.164 phone number for SMS test (default: first NOC_HD365_SMS)")
    ap.add_argument("--email-to", help="Email address for email test (default: MODERATE/URGENT/CRITICAL)")

    # OLT info toggles
    ap.add_argument("--olt-id", action="store_true", help="Print the parent OLT's ID for the given device_id")
    ap.add_argument("--olt-name", action="store_true", help="Print the parent OLT's name for the given device_id")
    ap.add_argument("--olt-json", action="store_true", help="Print the full parent OLT device JSON")

    # NEW: full record output
    ap.add_argument("--complete", action="store_true",
                    help="Print client_name, onu_id, onu_name, client_street, client_email, sms_notify/email_notify, client phone/email")

    args = ap.parse_args()

    settings = Settings()
    client = _build_client(settings)

    # Ticket-only mode
    if args.ticket_only:
        test_ticket_create_close(client, args.ticket_only)
        return

    mapped = None
    if args.device_id:
        try:
            mapped = test_mapping(client, args.device_id)
        except Exception as e:
            print(f"[WARN] mapping failed for {args.device_id}: {e}")
    else:
        print("\n=== SMOKE: NSM → CRM mapping ===")
        print("[SKIP] no device id provided")

    # OLT info
    if (args.olt_id or args.olt_name or args.olt_json):
        if not args.device_id:
            print("[ERR] --olt-* flags require a device_id (ONU GUID).")
            return
        oid, oname, od = resolve_olt_for_onu(client, args.device_id)
        if args.olt_id:
            print(f"OLT_ID={oid}")
        if args.olt_name:
            print(f"OLT_NAME={oname}")
        if args.olt_json:
            print(json.dumps(od, indent=2, sort_keys=True))

        # If ONLY OLT info was requested, exit early
        if not (args.complete or args.ticket_client or args.ticket_only or args.sms_to or args.email_to):
            return

    # COMPLETE info
    if args.complete:
        if not args.device_id:
            print("[ERR] --complete requires a device_id (ONU GUID).")
            return
        try:
            rec = resolve_complete_info(client, args.device_id)
            # Print in deterministic order
            keys = [
                "REGION",  # <-- NEW, first
                "ONU_ID", "ONU_NAME",
                "OLT_ID", "OLT_NAME",
                "CLIENT_ID", "CLIENT_NAME",
                "CLIENT_STREET", "CLIENT_CITY", "CLIENT_ZIP",
                "CLIENT_EMAIL", "CLIENT_PHONE",
                "PREF_SMS_NOTIFY", "PREF_EMAIL_NOTIFY",
            ]
            for k in keys:
                print(f"{k}={rec.get(k, '-')}")
        except Exception as e:
            print(f"[ERR] --complete failed: {e}")

        # Exit early if this was all the user asked for
        if not (args.ticket_client or args.sms_to or args.email_to):
            return

    # Ticket create/close if we know a client id (unchanged)
    t_client = args.ticket_client
    if t_client is None and mapped and mapped.get("crm_client_id"):
        t_client = int(mapped["crm_client_id"])
    if t_client:
        test_ticket_create_close(client, t_client)
    else:
        print("\n=== SMOKE: CRM ticket create + close ===")
        print("[SKIP] no client id available (pass --ticket-client or provide a mappable device)")

    # Determine text/email recipients (unchanged)
    sms_to = args.sms_to
    if not sms_to:
        sms_list = getattr(settings, "NOC_HD365_SMS", []) or []
        sms_to = sms_list[0] if sms_list else None

    email_to = args.email_to
    if not email_to:
        email_to = (
            getattr(settings, "MODERATE_MAIL", "") or
            getattr(settings, "URGENT_MAIL", "") or
            getattr(settings, "CRITICAL_MAIL", "")
        )

    name = (mapped or {}).get("name") or "-"
    address =(mapped or {}).get("address") or "-"
    dev = args.device_id or "-"

    # 3) SMS
    test_sms(sms_to, name, address, dev)

    # 4) Email
    test_email(email_to, name, address, dev)


if __name__ == "__main__":
    main()
