from __future__ import annotations
import time
import json
from datetime import datetime, timezone, date
from typing import Iterable, Optional, List, Dict, Any
try:
    from zoneinfo import ZoneInfo  # py3.9+
except Exception:  # pragma: no cover
    ZoneInfo = None  # fallback

DEFAULT_TZ = "America/New_York"

def _tz():
    try:
        return ZoneInfo(DEFAULT_TZ) if ZoneInfo else None
    except Exception:
        return None

def ny_human(ts: int) -> str:
    """Format epoch seconds in America/New_York as 'YYYY-MM-DD HH:MM:SS EDT'."""
    tz = _tz()
    dt = datetime.fromtimestamp(int(ts), tz) if tz else datetime.fromtimestamp(int(ts))
    tzname = dt.tzname() or "local"
    return dt.strftime(f"%Y-%m-%d %H:%M:%S {tzname}")

def human_duration(start_ts: int, end_ts: Optional[int] = None) -> str:
    """Return compact human duration like '2h 3m', '1d 2h', '45s'."""
    end = int(end_ts) if end_ts is not None else int(time.time())
    total = max(0, int(end) - int(start_ts))
    d, rem = divmod(total, 86400)
    h, rem = divmod(rem, 3600)
    m, s = divmod(rem, 60)
    parts = []
    if d: parts.append(f"{d}d")
    if h: parts.append(f"{h}h")
    if m: parts.append(f"{m}m")
    if s or not parts: parts.append(f"{s}s")
    return " ".join(parts)

class Messaging:
    """
    Central text/templates for SMS, email, ticket comments, daily status digests, and logs.
    Pure formatting: no I/O, no HTTP, no DB.
    """
    def __init__(self, *, test_mode: bool = False, brand: str = "ESVC"):
        self.test_mode = bool(test_mode)
        self.brand = (brand or "ESVC").strip()

    def _prefix(self, s: str) -> str:
        return f"[TEST] {s}" if self.test_mode else s

    # -------- customer-friendly text (no UIDs) --------
    def fmt_customer_outage(*, name: str | None, address: str | None, started_at_iso: str | None) -> Tuple[str, str]:
        subject = "Service Interruption Detected"
        who = (name or "Customer")
        lines = [
            f"Hi {who},",
            "",
            "We detected an interruption to your internet service.",
            "You do not need to reboot your equipment unless we ask you to.",
            "Our team is investigating and working to restore service.",
        ]
        if address:
            lines.append(f"Service location: {address}")
        if started_at_iso:
            lines.append(f"Detected at: {started_at_iso} (local)")
        lines += [
            "",
            "We’ll follow up when service is restored. Thank you for your patience."
        ]
        return subject, "\n".join(lines)

    def fmt_customer_recovery(*, name: str | None) -> Tuple[str, str]:
        subject = "Service Restored"
        who = (name or "Customer")
        body = "\n".join([
            f"Hi {who},",
            "",
            "Your internet service is back online.",
            "If you still have issues, power your router off and back on once, wait 2–3 minutes, and try again.",
            "",
            "Thanks for being our customer!"
        ])
        return subject, body

    # -------- staff/NOC/field (human-readable, rich context) --------
    def _short_age(sec: float | None) -> str:
        if sec is None: return "-"
        sec = int(max(0, sec))
        m, s = divmod(sec, 60);
        h, m = divmod(m, 60)
        return f"{h}h{m:02d}m" if h else f"{m}m{s:02d}s"

    def fmt_staff_cluster_outage(
            *,
            region_label: str,
            site_label: str | None,
            olt_label: str | None,
            count: int,
            oldest_age_s: float,
            fs3900: Optional[Dict[str, Any]],
    ) -> Tuple[str, str]:
        title_site = f"{region_label.upper()}" + (f" / {site_label}" if site_label else "")
        subject = f"[OUTAGE][{title_site}] OLT impact ~{count} ONUs"
        lines = [
            f"Region/Site : {title_site}",
            f"OLT        : {olt_label or '(unknown)'}",
            f"Affected   : ~{count} ONUs",
            f"Oldest age : {_short_age(oldest_age_s)}",
        ]
        # FS3900 snapshot (if present)
        if fs3900:
            if "error" in fs3900:
                lines += ["", f"FS3900 Probe : ERROR {fs3900['error']}"]
            else:
                lines += ["", f"FS3900 Probe : host={fs3900.get('host')} ({fs3900.get('elapsed_s')}s)"]
                for p in (fs3900.get("ports") or []):
                    port = p.get("port");
                    oper = p.get("oper_status")
                    dom = p.get("dom") or {}
                    thr = p.get("metric_severity") or {}
                    traffic = p.get("traffic") or {}
                    in_bps = traffic.get("in_bps");
                    out_bps = traffic.get("out_bps")
                    sev = p.get("worst_severity")
                    lines.append(
                        f"  Port {port:<2} oper={oper:<8} rx={dom.get('rx_dbm')} dBm "
                        f"tx={dom.get('tx_dbm')} dBm vcc={dom.get('vcc_v')} V temp={dom.get('temp_c')} C "
                        f"sev={sev:<6} in={int(in_bps or 0)}bps out={int(out_bps or 0)}bps"
                    )
        lines.append("")
        lines.append("Next steps: verify uplink power/LOS on OLT, check peer links, and upstream reachability.")
        return subject, "\n".join(lines)

    def fmt_staff_cluster_recovery(
            *,
            region_label: str,
            site_label: str | None,
            olt_label: str | None,
            count: int,
    ) -> Tuple[str, str]:
        title_site = f"{region_label.upper()}" + (f" / {site_label}" if site_label else "")
        subject = f"[RECOVERY][{title_site}] OLT recovered (~{count} ONUs)"
        body = "\n".join([
            f"Region/Site : {title_site}",
            f"OLT        : {olt_label or '(unknown)'}",
            f"Recovered  : ~{count} ONUs",
            "",
            "Monitor briefly for flap, then close tickets if stable."
        ])
        return subject, body

    # ---------- CUSTOMER (end-user) ----------
    def customer_outage_sms(self, endpoint_name: str) -> str:
        # Short & calm; no address in SMS
        return self._prefix(f"[{self.brand}] Service interruption detected for {endpoint_name}.")

    def customer_restore_sms(self) -> str:
        return self._prefix(f"[{self.brand}] Service has been restored. Thank you for your patience.")

    def customer_outage_email_subject(self, endpoint_name: str) -> str:
        return self._prefix(f"[{self.brand}] Service interruption detected ({endpoint_name})")

    def customer_outage_email_body(self, endpoint_name: str) -> str:
        return (
            f"Hello,\n\n"
            f"We detected a temporary service interruption for {endpoint_name}. "
            f"Our team is investigating and will restore service as soon as possible.\n\n"
            f"— {self.brand} Support"
        )

    def customer_restore_email_subject(self, endpoint_name: str) -> str:
        return self._prefix(f"[{self.brand}] Service restored ({endpoint_name})")

    def customer_restore_email_body(self, endpoint_name: str) -> str:
        return (
            f"Hello,\n\n"
            f"Service has been restored for {endpoint_name}. Thank you for your patience.\n\n"
            f"— {self.brand} Support"
        )

    # ---------- STAFF (NOC/helpdesk/field) ----------
    def staff_outage_sms(self, name: str, address: str, started_ts: int) -> str:
        return self._prefix(
            f"[{self.brand}] {name} — {address or '-'} down since {ny_human(started_ts)} "
            f"({human_duration(started_ts)})"
        )

    def staff_restore_sms(self, name: str, address: str, started_ts: int, closed_ts: int) -> str:
        return self._prefix(
            f"[{self.brand}] {name} — {address or '-'} restored at {ny_human(closed_ts)} "
            f"(total {human_duration(started_ts, closed_ts)})"
        )

    def staff_olt_sms(self, olt_id: str, severity: str, affected: int, confidence: float) -> str:
        sev = severity.upper()
        return self._prefix(f"[{self.brand}] {sev} on OLT {olt_id}: {affected} ONUs (C={confidence:.0f}).")

    def staff_cluster_subject(self, severity: str) -> str:
        sev = severity.upper()
        return self._prefix(f"[{self.brand}] {sev} outage")

    def staff_cluster_body(self, rows: Iterable[dict]) -> str:
        # rows: iterator of {endpoint, client_id, client_addr}
        lines = []
        for r in rows:
            lines.append(
                f"* {r.get('endpoint') or r.get('endpoint_name') or '(unknown)'} — "
                f"Client #{r.get('client_id') or '-'} — {r.get('client_addr') or '-'}"
            )
        return "\n".join(lines) or "No current customer outages."

    # ---------- Tickets: activity comment text ----------
    def ticket_open_comment(self, endpoint_name: str, onu_id: str, address: str, started_ts: int, extra_note: str | None = None) -> str:
        base = (
            f"Outage detected for {endpoint_name} (ONU {onu_id}) at {ny_human(started_ts)}.\n"
            f"Address: {address or '-'}\n"
            f"Duration so far: {human_duration(started_ts)}"
        )
        if extra_note:
            base += f"\n{extra_note}"
        return base

    def ticket_close_comment(self, started_ts: int, closed_ts: int, note: str | None = None) -> str:
        msg = f"Service restored at {ny_human(closed_ts)}. Total duration: {human_duration(started_ts, closed_ts)}."
        if note:
            msg += f" {note}"
        return msg

    # --- add near ny_human/human_duration in messaging.py ---

    def ny_time(ts: int) -> str:
        """
        NY-local time only, like '3:15pm' (no leading zero, lowercase am/pm).
        """
        tz = _tz()
        dt = datetime.fromtimestamp(int(ts), tz) if tz else datetime.fromtimestamp(int(ts))
        s = dt.strftime("%I:%M%p").lstrip("0").lower()
        return s

    def _nice_date(d: date) -> str:
        """
        'Monday August 25, 2025' (no leading zero for day).
        """
        # strftime portable trick to drop day leading zero
        s = d.strftime("%A %B %d, %Y")
        return s.replace(" 0", " ")

    # ---------- Daily Status Digest ----------
    def daily_status_subject(self, day: date) -> str:
        # NOTE: we intentionally *don’t* include brackets/brand prefix here
        # beyond [TEST] – subject should read like:
        # "ESVC Daily Network Status for Monday August 25, 2025"
        title = f"{self.brand} Daily Network Status for {_nice_date(day)}"
        return self._prefix(title)

    def daily_status_body(
            self,
            *,
            outages: List[Dict[str, Any]],
            clusters: List[Dict[str, Any]],
            notes: Optional[str] = None,
    ) -> str:
        """
        Render the daily digest body. Assumes the email subject already contains the date,
        so rows show time-of-day only.
        """

        def _time_only(ts: int | None) -> str:
            if not ts:
                return "-"
            tz = _tz()
            dt = datetime.fromtimestamp(int(ts), tz) if tz else datetime.fromtimestamp(int(ts))
            try:
                s = dt.strftime("%-I:%M%p")  # e.g. 3:15PM
            except Exception:
                s = dt.strftime("%I:%M%p").lstrip("0") or "12:00AM"
            return s.lower()  # 3:15pm

        parts: List[str] = []

        # ---- Customer outages ----
        parts.append("Customer outages:")
        if outages:
            headers = ["Customer", "Address", "Start", "End", "Duration", "Ticket"]
            rows: List[List[str]] = []
            outages = sorted(outages, key=lambda o: int(o.get("start_ts") or 0))
            for o in outages:
                start = _time_only(int(o.get("start_ts") or 0))
                stop_ts = o.get("stop_ts")
                stop = _time_only(int(stop_ts)) if stop_ts else "-"
                # duration already computed by tasks for ongoing bounded to day_end; safe to recompute
                dur = human_duration(int(o.get("start_ts") or 0), int(stop_ts) if stop_ts else None)
                rows.append([
                    str(o.get("endpoint") or "-"),
                    str(o.get("address") or "-"),
                    start,
                    stop if stop_ts else "-",
                    dur,
                    str(o.get("ticket_id") or "-"),
                ])
            parts.append(self._tabular(headers, rows))
        else:
            parts.append("  None recorded.")

        parts.append("")  # spacer

        # ---- Cluster events ----
        parts.append("Cluster events:")
        if clusters:
            headers = ["OLT", "Impacted", "Start", "End", "Duration"]
            rows: List[List[str]] = []
            clusters = sorted(clusters, key=lambda c: int(c.get("start_ts") or 0))
            for c in clusters:
                s_ts = int(c.get("start_ts") or 0)
                e_ts = int(c.get("stop_ts")) if c.get("stop_ts") else None
                rows.append([
                    ", ".join(c.get("olts") or []),
                    str(c.get("impacted_count") or 0),
                    _time_only(s_ts) if s_ts else "-",
                    _time_only(e_ts) if e_ts else "-",
                    human_duration(s_ts, e_ts if e_ts else None),
                ])
            parts.append(self._tabular(headers, rows))
        else:
            parts.append("  None recorded.")

        if notes:
            parts.append("")
            parts.append("Notes:")
            parts.append(notes)

        return "\n".join(parts)

    def _fmt_bool(self, v: Any) -> str:
        return "Y" if bool(v) else "N"

    # messaging.py — REPLACE _tabular with this grid-style version
    def _tabular(self, headers, rows, aligns=None, max_widths=None) -> str:
        """
        Plain-text grid table that survives proportional fonts.
        - Uses | separators and +---+ rules.
        - Left-align text columns (Customer, Address, OLT); right-align times/numbers.
        - Truncates wide cells with … to keep lines tidy in email clients.
        """
        headers = ["" if h is None else str(h) for h in headers]
        rows = [[("" if c is None else str(c)) for c in r] for r in rows]
        ncol = len(headers)

        # Heuristic alignment
        if aligns is None:
            aligns = []
            for h in headers:
                hl = h.lower()
                aligns.append("left" if any(k in hl for k in ("customer", "address", "olt")) else "right")

        # Sensible caps for email (tweak if you want wider/narrower)
        if max_widths is None:
            max_widths = []
            for h in headers:
                hl = h.lower()
                if "address" in hl:
                    cap = 40
                elif "customer" in hl or hl == "olt":
                    cap = 20
                elif "duration" in hl:
                    cap = 9
                elif "start" in hl or "end" in hl:
                    cap = 7  # e.g., 10:16pm
                elif "ticket" in hl:
                    cap = 6
                else:
                    cap = 12
                max_widths.append(cap)

        def trunc(s: str, cap: int) -> str:
            return s if len(s) <= cap else (s[: max(0, cap - 1)] + "…")

        # Compute actual widths after truncation
        widths = []
        for i in range(ncol):
            cap = max_widths[i]
            best = len(trunc(headers[i], cap))
            for r in rows:
                cell = trunc(r[i], cap) if i < len(r) else ""
                if len(cell) > best:
                    best = len(cell)
            widths.append(best)

        def fmt_cell(i: int, s: str) -> str:
            s = trunc(s, max_widths[i])
            return s.ljust(widths[i]) if aligns[i] == "left" else s.rjust(widths[i])

        # Build lines
        def rule(char="-"):
            return "+" + "+".join(char * (w + 2) for w in widths) + "+"

        header_line = "|" + "|".join(" " + fmt_cell(i, headers[i]) + " " for i in range(ncol)) + "|"
        row_lines = []
        for r in rows:
            row_lines.append(
                "|" + "|".join(" " + fmt_cell(i, r[i] if i < len(r) else "") + " " for i in range(ncol)) + "|")

        # Assemble grid with a single header rule (no heavy box madness)
        lines = [rule("-"), header_line, rule("-"), *row_lines, rule("-")]
        return "\n".join(l.rstrip() for l in lines)


def _trim_sms(s: str, limit: int = 160) -> str:
    return s if len(s) <= limit else (s[: limit - 1] + "…")

def context_one_liner(ctx: Dict[str, Any]) -> str:
    # WL · NC-CMD-WL-UF-OLT4 · Thomas Sweet · 101 Black Bear Way · ONU Sweet_Thomas-… · 6485e2…
    parts = [
        ctx.get("REGION", "-"),
        ctx.get("OLT_NAME", "-"),
        ctx.get("CLIENT_NAME", "-"),
        ctx.get("CLIENT_STREET", "-"),
        f"ONU {ctx.get('ONU_NAME','-')}",
        (ctx.get("ONU_ID","-") or "-")[:8],
    ]
    return " · ".join(x for x in parts if x and x != "-")

def context_block(ctx: Dict[str, Any]) -> str:
    lines = [
        f"Region:      {ctx.get('REGION','-')}",
        f"OLT:         {ctx.get('OLT_NAME','-')} ({ctx.get('OLT_ID','-')})",
        f"Client:      {ctx.get('CLIENT_NAME','-')} (id={ctx.get('CLIENT_ID','-')})",
        f"Address:     {ctx.get('CLIENT_STREET','-')}, {ctx.get('CLIENT_CITY','-')} {ctx.get('CLIENT_ZIP','-')}",
        f"Contact:     {ctx.get('CLIENT_PHONE','-')} | {ctx.get('CLIENT_EMAIL','-')}",
        f"Prefs:       sms={ctx.get('PREF_SMS_NOTIFY','unknown')}, email={ctx.get('PREF_EMAIL_NOTIFY','unknown')}",
        f"ONU:         {ctx.get('ONU_NAME','-')} ({ctx.get('ONU_ID','-')})",
    ]
    return "\n".join(lines)

class MessagingV2(Messaging):
    """
    Enriched message builders that accept a context dict (backwards-compatible subclass).
    """

    # Staff: OLT cluster page with a sample context line (still short)
    def staff_olt_sms_enriched(self, severity: str, affected: int, confidence: float, olt_label: str, ctx: Dict[str,Any] | None = None) -> str:
        sev = severity.upper()
        base = f"[{self.brand}] {sev} on {olt_label}: {affected} ONUs (C={confidence:.0f})"
        if ctx:
            extra = context_one_liner(ctx)
            return self._prefix(_trim_sms(f"{base} — {extra}"))
        return self._prefix(_trim_sms(base))

    # Customer: outage
    def customer_sms_outage(self, ctx: Dict[str,Any], eta: str | None = None) -> str:
        # Keep within 160 incl brand; include opt-out keyword only if your policy wants it in each msg
        line = f"[{self.brand}] Service outage in {ctx.get('REGION','your area')}. We’re working on it."
        who = f" {ctx.get('CLIENT_NAME','')}".strip()
        hint = f" OLT {ctx.get('OLT_NAME','-')}"
        tail = f" ETA {eta}." if eta else ""
        return _trim_sms(self._prefix(f"{line}{hint}.{tail}"))

    # Customer: restoration
    def customer_sms_restore(self, ctx: Dict[str,Any], started_ts: int, closed_ts: int) -> str:
        dur = human_duration(started_ts, closed_ts)
        base = f"[{self.brand}] Service restored in {ctx.get('REGION','your area')} (total {dur})."
        return _trim_sms(self._prefix(base))

    # Ticket subject/body for single-customer incidents (your policy)
    def ticket_subject_outage(self, ctx: Dict[str,Any]) -> str:
        return self._prefix(f"Outage — {ctx.get('CLIENT_NAME','-')} @ {ctx.get('CLIENT_STREET','-')} [{ctx.get('REGION','-')}]")

    def ticket_body_outage(self, ctx: Dict[str,Any], started_ts: int) -> str:
        opened = ny_human(started_ts)
        lines = [
            f"Opened:      {opened}",
            context_block(ctx),
            "",
            "Observation:",
            f"- Detected ONU offline ({ctx.get('ONU_ID','-')}).",
            f"- Parent OLT: {ctx.get('OLT_NAME','-')} ({ctx.get('OLT_ID','-')}).",
        ]
        return "\n".join(lines)

    def ticket_update_restore(self, ctx: Dict[str,Any], started_ts: int, closed_ts: int) -> str:
        return f"Restored at {ny_human(closed_ts)} (total {human_duration(started_ts, closed_ts)}).\n\n{context_block(ctx)}"