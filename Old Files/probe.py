# probe.py
from __future__ import annotations

import os
import shlex
import socket
import subprocess, time, re
from dataclasses import dataclass
from typing import Optional, Tuple, Dict, Any

from config import settings  # matches your current config

# -----------------------------------------------------------------------------
# Small utils
# -----------------------------------------------------------------------------

def _which(cmd: str) -> Optional[str]:
    for p in os.getenv("PATH", "").split(os.pathsep):
        cand = os.path.join(p, cmd)
        if os.path.isfile(cand) and os.access(cand, os.X_OK):
            return cand
    return None

def _run(argv: list[str], timeout_s: float = 2.5) -> Tuple[int, str, str]:
    """Run a command and return (rc, stdout, stderr)."""
    try:
        out = subprocess.run(argv, capture_output=True, text=True, timeout=timeout_s)
        return out.returncode, out.stdout or "", out.stderr or ""
    except subprocess.TimeoutExpired as e:
        return 124, "", f"timeout after {timeout_s}s: {e}"
    except Exception as e:
        return 1, "", f"{type(e).__name__}: {e}"

# -----------------------------------------------------------------------------
# Ping (best-effort)
# -----------------------------------------------------------------------------

_FPING = _which("fping")
_PING  = _which("ping")

def _ping_one(host: str, timeout_ms: int = 800) -> Optional[bool]:
    """
    Return True if host is reachable, False if not, None if we couldn't test.
    """
    if not host:
        return None

    if _FPING:
        # fping returns 0 if alive, 1 if not, 2 for other errors
        rc, out, err = _run([_FPING, "-t", str(timeout_ms), "-c1", "-q", host], timeout_s=max(1.0, timeout_ms/1000+0.5))
        if rc in (0, 1):
            return (rc == 0)
        return None

    if _PING:
        # Linux/Unix ping: one probe, deadline ~timeout, quiet output
        deadline = max(1, int(round(timeout_ms/1000)))
        rc, out, err = _run([_PING, "-c", "1", "-W", str(deadline), host], timeout_s=deadline+1)
        if rc in (0, 1):
            return (rc == 0)
        return None

    # Last resort: TCP 80 connect check
    try:
        with socket.create_connection((host, 80), timeout=max(0.5, timeout_ms/1000)):
            return True
    except Exception:
        return False

# -----------------------------------------------------------------------------
# SNMP helpers (net-snmp CLI, no new envs)
# -----------------------------------------------------------------------------

def _snmpget_str(host: str, community: str, oid: str, timeout_s: float = 1.5) -> Optional[str]:
    """
    Return the -Oqv textual value for a single OID or None on error.
    """
    if not host or not oid:
        return None
    cmd = ["snmpget", "-v2c", "-On", "-Oqv", "-c", community, host, oid]
    rc, out, err = _run(cmd, timeout_s=timeout_s)
    if rc == 0:
        return (out or "").strip()
    return None

def _snmpwalk_pairs(host: str, community: str, root_oid: str, timeout_s: float = 2.5) -> dict[str, str]:
    """
    Return {leaf_oid: value_text} for a subtree.
    """
    if not host or not root_oid:
        return {}
    cmd = ["snmpwalk", "-v2c", "-On", "-Oqv", "-c", community, host, root_oid]
    rc, out, err = _run(cmd, timeout_s=timeout_s)
    if rc != 0:
        return {}
    res = {}
    # With -Oqv we lose the left side; do a non -Oqv pass if you need OIDs.
    # For our lookups we usually want textual values only.
    # If you do need OIDs for mapping, switch to -On and split.
    for ln in (out or "").splitlines():
        # -Oqv only prints values; this walker is value-only helper
        # We keep it here for symmetry; we don't rely on OIDs from this.
        pass
    return {}

# -----------------------------------------------------------------------------
# IF-MIB helpers
# -----------------------------------------------------------------------------

_IFNAME_OID  = ".*******.********.1.1.1"   # ifName
_IFDESCR_OID = ".*******.*******.1.2"      # ifDescr
_IFOPER_OID  = ".*******.*******.1.8"      # ifOperStatus (1=up,2=down,3=testing...)

def _snmpwalk_kv(host: str, community: str, root: str, timeout_s: float = 2.5) -> dict[int, str]:
    """
    Return {ifIndex: text} by walking root (-On, parse numeric index).
    """
    if not host or not root:
        return {}
    cmd = ["snmpwalk", "-v2c", "-On", "-c", community, host, root]
    rc, out, err = _run(cmd, timeout_s=timeout_s)
    if rc != 0:
        return {}
    res: dict[int, str] = {}
    for ln in (out or "").splitlines():
        # Example: .*******.********.******** = STRING: ethernet1/27
        try:
            left, right = ln.split("=", 1)
            left = left.strip()
            idx = int(left.rsplit(".", 1)[-1])
            # strip 'STRING:' etc.
            val = right.split(":", 1)[-1].strip()
            # remove surrounding quotes if present
            if val.startswith('"') and val.endswith('"'):
                val = val[1:-1]
            res[idx] = val
        except Exception:
            continue
    return res

def _find_ifindex_by_name(host: str, community: str, ifname: str) -> Optional[int]:
    """
    Resolve ifIndex by matching ifName or ifDescr ≈ ifname (case/spacing tolerant).
    """
    if not host or not ifname:
        return None

    def norm(s: str) -> str:
        # lower, drop spaces and non-alnum except '/', so "ethernet 1/27" == "Ethernet1/27"
        return re.sub(r"[^a-z0-9/]", "", (s or "").lower())

    target = norm(ifname)

    m1 = _snmpwalk_kv(host, community, _IFNAME_OID)
    for k, v in m1.items():
        if norm(v) == target:
            return k

    m2 = _snmpwalk_kv(host, community, _IFDESCR_OID)
    for k, v in m2.items():
        if norm(v) == target:
            return k

    return None


def _if_oper_status(host: str, community: str, ifindex: int) -> str:
    """
    Return 'up'|'down'|'unknown' from ifOperStatus.
    """
    s = _snmpget_str(host, community, f"{_IFOPER_OID}.{ifindex}")
    if not s:
        return "unknown"
    try:
        code = int(str(s).strip())
        return "up" if code == 1 else ("down" if code == 2 else "unknown")
    except Exception:
        return "unknown"

# -----------------------------------------------------------------------------
# IF-MIB octets → bps sampler
# -----------------------------------------------------------------------------

_IF_IN_OCT  = ".*******.*******.1.10"  # ifInOctets
_IF_OUT_OCT = ".*******.*******.1.16"  # ifOutOctets

def _if_octets_bps(host: str, community: str, ifindex: int, sample_ms: int = 1500) -> tuple[Optional[float], Optional[float], Optional[int]]:
    """
    Sample in/out octets over sample_ms and return (in_bps, out_bps, dt_ms).
    """
    def read_pair() -> tuple[Optional[int], Optional[int]]:
        a = _snmpget_str(host, community, f"{_IF_IN_OCT}.{ifindex}")
        b = _snmpget_str(host, community, f"{_IF_OUT_OCT}.{ifindex}")
        try:
            return int(a), int(b)
        except Exception:
            return None, None

    a1, b1 = read_pair()
    if a1 is None or b1 is None:
        return None, None, None
    t1 = time.monotonic_ns()
    time.sleep(max(0.2, sample_ms / 1000))
    a2, b2 = read_pair()
    t2 = time.monotonic_ns()
    if a2 is None or b2 is None:
        return None, None, None

    dt_ms = int((t2 - t1) / 1_000_000)
    if dt_ms <= 0:
        return None, None, None

    din  = (a2 - a1) if a2 >= a1 else (a2 + (1 << 32) - a1)  # handle wrap (32-bit)
    dout = (b2 - b1) if b2 >= b1 else (b2 + (1 << 32) - b1)

    in_bps  = (din  * 8_000.0) / dt_ms
    out_bps = (dout * 8_000.0) / dt_ms
    return in_bps, out_bps, dt_ms

def _if_octets(host: str, community: str, ifindex: int, sample_ms: int = 1500) -> dict[str, Optional[float]]:
    """
    Back-compat wrapper over _if_octets_bps that returns a dict with keys the rest
    of the code expects.
    """
    in_bps, out_bps, dt_ms = _if_octets_bps(host, community, ifindex, sample_ms=sample_ms)
    return {
        "in_bps": in_bps,
        "out_bps": out_bps,
        "dt_ms": dt_ms,
        # keep 'elapsed_ms' too, in case any legacy path still reads it
        "elapsed_ms": dt_ms,
    }

# -----------------------------------------------------------------------------
# FS3900 DOM (ports 25–28 only)
# -----------------------------------------------------------------------------

# FS enterprise OIDs per your examples (port = ifIndex suffix)
#  .*******.4.1.52642.********.********.<port> => "35.36 degrees C normal"
#  .*******.4.1.52642.********.********.<port> => "3.34 V normal"
#  .*******.4.1.52642.********.********.<port> => "78.49 mA normal"
#  .*******.4.1.52642.********.********.<port> => "2.33 dBm normal"   (TX)
#  .*******.4.1.52642.********.********.<port> => "-2.60 dBm normal"  (RX)

_RX_OID = ".*******.4.1.52642.********.********"
_TX_OID = ".*******.4.1.52642.********.********"
_TC_OID = ".*******.4.1.52642.********.********"
_VC_OID = ".*******.4.1.52642.********.********"
_BI_OID = ".*******.4.1.52642.********.********"

_DBM_RE = re.compile(r"(-?\d+(?:\.\d+)?)\s*dBm", re.I)
_V_RE   = re.compile(r"(\d+(?:\.\d+)?)\s*V\b", re.I)
_MA_RE  = re.compile(r"(\d+(?:\.\d+)?)\s*mA\b", re.I)
_C_RE   = re.compile(r"(-?\d+(?:\.\d+)?)\s*degree", re.I)

def _parse_num(pat: re.Pattern, text: Optional[str]) -> Optional[float]:
    if not text:
        return None
    m = pat.search(text)
    return float(m.group(1)) if m else None

def _get_dom_thresholds(fs_ip: str, ifindex: int) -> dict:
    """
    Load thresholds with per-(model,fs_ip,ifindex) override then model fallback.
    Your config exposes the two maps we need.
    """
    # model is fixed here (fs3900). If you add s5800 later, you’ll branch here.
    model = "fs3900"
    # device/port specific
    spec = settings.DOM_THRESHOLDS.get((model, str(fs_ip), int(ifindex)))
    if spec:
        return spec
    # model defaults
    return settings.MODEL_DOM_DEFAULTS.get(model, {})

@dataclass(frozen=True)
class _DomRead:
    rx_dbm: Optional[float]
    tx_dbm: Optional[float]
    temp_c: Optional[float]
    vcc_v: Optional[float]
    bias_ma: Optional[float]
    state: Optional[str]
    threshold_source: str

def _fs3900_read_dom(fs_ip: str, snmp_community: str, ifname_or_ifindex) -> dict:
    """
    Read FS3900 DOM for ports 25..28. FS exposes DOM under:
      .*******.4.1.52642.********.2.11.1.{2=temp,3=vcc,4=bias,5=tx,6=rx}.<port>

    We accept either the ifname ("ethernet 1/26") or a port int. If an SNMP
    ifIndex (not a port number) is passed, this will return {} (by design).
    """
    # figure out port number
    port: Optional[int] = None
    if isinstance(ifname_or_ifindex, str):
        m = re.search(r"(\d+)$", ifname_or_ifindex.strip())
        port = int(m.group(1)) if m else None
    elif isinstance(ifname_or_ifindex, int):
        # treat small ints as direct port numbers, ignore large IF-MIB indices
        port = ifname_or_ifindex if 1 <= ifname_or_ifindex <= 128 else None

    if port is None or port not in (25, 26, 27, 28):
        return {}

    # OIDs from your working walks
    rx_s = _snmpget_str(fs_ip, snmp_community, f"{_RX_OID}.{port}")
    tx_s = _snmpget_str(fs_ip, snmp_community, f"{_TX_OID}.{port}")
    tc_s = _snmpget_str(fs_ip, snmp_community, f"{_TC_OID}.{port}")
    vc_s = _snmpget_str(fs_ip, snmp_community, f"{_VC_OID}.{port}")
    bi_s = _snmpget_str(fs_ip, snmp_community, f"{_BI_OID}.{port}")

    rx = _parse_num(_DBM_RE, rx_s)
    tx = _parse_num(_DBM_RE, tx_s)
    tc = _parse_num(_C_RE,   tc_s)
    vc = _parse_num(_V_RE,   vc_s)
    bi = _parse_num(_MA_RE,  bi_s)

    # thresholds: per-device/port override, else model default
    model = "fs3900"
    th_map = settings.DOM_THRESHOLDS.get((model, str(fs_ip), int(port)))
    if not th_map:
        th_map = settings.MODEL_DOM_DEFAULTS.get(model, {})

    rx_th = th_map.get("rx_dbm") if th_map else None
    dom_low_alarm_dbm = rx_th.get("low_alarm") if rx_th else None
    dom_low_warn_dbm  = rx_th.get("low_warn")  if rx_th else None
    dom_high_warn_dbm = rx_th.get("high_warn") if rx_th else None
    dom_high_alarm_dbm= rx_th.get("high_alarm")if rx_th else None

    # classify rx: normal / caution / alarm
    dom_rx_state = None
    if rx is not None and dom_low_alarm_dbm is not None and dom_low_warn_dbm is not None:
        if rx <= dom_low_alarm_dbm:
            dom_rx_state = "alarm"
        elif rx <= dom_low_warn_dbm:
            dom_rx_state = "caution"
        else:
            dom_rx_state = "normal"

    return {
        "dom_source": "fs3900.dom.********",
        "dom_threshold_source": (
            "settings.DOM_THRESHOLDS" if ((model, str(fs_ip), int(port)) in settings.DOM_THRESHOLDS)
            else "settings.MODEL_DOM_DEFAULTS"
        ),
        "dom_rx_dbm": rx,
        "dom_tx_dbm": tx,
        "dom_temp_c": tc,
        "dom_vcc_v":  vc,
        "dom_bias_ma": bi,
        "dom_low_alarm_dbm": dom_low_alarm_dbm,
        "dom_low_warn_dbm":  dom_low_warn_dbm,
        "dom_high_warn_dbm": dom_high_warn_dbm,
        "dom_high_alarm_dbm":dom_high_alarm_dbm,
        "dom_rx_state": dom_rx_state,
    }


# -----------------------------------------------------------------------------
# Public API: probe_olt
# -----------------------------------------------------------------------------

def probe_olt(olt_id: str,
              name: str,
              olt_ip: str,
              fs_ip: str,
              fs_uplink_ifname: Optional[str],
              snmp_community: str,
              sample_ms: int = 1500,
              want_dom: bool = True) -> Dict[str, Any]:
    """
    Probe a single OLT + its FS uplink.

    Returns a dict with:
      - reachability.olt/fs (booleans)
      - fs_uplink_detail: ifname, status, in_bps, out_bps, dt_ms, has_traffic
                          + flat DOM keys when want_dom and ifindex in {25..28}
    """
    res: Dict[str, Any] = {
        "olt_ip": olt_ip,
        "fs_ip": fs_ip,
        "name": name,
        "summary": None,
        "reachability": {"olt": None, "fs": None},
        "fs_uplink_detail": {},
    }

    # reachability
    res["reachability"]["olt"] = _ping_one(olt_ip)
    res["reachability"]["fs"]  = _ping_one(fs_ip)

    # if uplink name is missing, abort early but keep reachability
    if not fs_uplink_ifname:
        res["summary"] = "No uplink ifname configured"
        return res

    # resolve ifIndex from ifName
    ifindex = _find_ifindex_by_name(fs_ip, snmp_community, fs_uplink_ifname)
    fsd: Dict[str, Any] = {"ifname": fs_uplink_ifname}
    res["fs_uplink_detail"] = fsd

    if not ifindex:
        fsd["status"] = "unknown"
        res["summary"] = f"ifIndex not found for {fs_uplink_ifname}"
        return res

    # operational status
    oper = _if_oper_status(fs_ip, snmp_community, ifindex)
    fsd["status"] = oper or "unknown"

    # traffic sample
    rates = _if_octets(fs_ip, snmp_community, ifindex, sample_ms=sample_ms)
    fsd["in_bps"] = rates.get("in_bps")
    fsd["out_bps"] = rates.get("out_bps")
    fsd["dt_ms"] = rates.get("dt_ms")  # <-- correct key

    try:
        total = float(fsd["in_bps"] or 0.0) + float(fsd["out_bps"] or 0.0)
        fsd["has_traffic"] = bool(total >= 100_000.0)
    except Exception:
        fsd["has_traffic"] = None

    # DOM: FS3900 DOM is keyed by port number (25..28) from the **ifname**, not the SNMP ifIndex
    if want_dom and oper == "up":
        dom = _fs3900_read_dom(fs_ip, snmp_community, fs_uplink_ifname)
        if dom:
            fsd.update(dom)

    # DOM
    if want_dom and oper == "up":
        dom = _fs3900_read_dom(fs_ip, snmp_community, ifindex)
        # FLAT: attach dom_* directly onto fs_uplink_detail
        if dom:
            fsd.update(dom)

    # summary
    res["summary"] = f"OLT:{'UP' if res['reachability']['olt'] else 'DOWN' if res['reachability']['olt'] is False else 'UNKNOWN'} " \
                     f"FS:{'UP' if res['reachability']['fs'] else 'DOWN' if res['reachability']['fs'] is False else 'UNKNOWN'} " \
                     f"{fs_uplink_ifname}:{fsd.get('status','unknown')}"
    return res
