# test_harness.py
"""
Run the probe and rollups locally, without Celery workers,
to verify end-to-end integration with FS3900Probe + storage.
"""

import json
import logging
from tasks import probe_devices_flat_and_persist, rollup_5m, rollup_30m  # noqa: F401

logging.basicConfig(level=logging.INFO)

print(">>> probe_devices_flat_and_persist()")
snap = probe_devices_flat_and_persist()  # type: ignore[misc]
print(json.dumps(snap, indent=2, sort_keys=True, default=str))

print("\n>>> rollup_5m()")
rollup_5m()  # type: ignore[misc]
print("5m rollup ok")

print("\n>>> rollup_30m()")
rollup_30m()  # type: ignore[misc]
print("30m rollup ok")
