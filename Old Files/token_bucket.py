# token_bucket.py
import time

class TokenBucket:
    def __init__(self, rate_per_min: int, capacity: int | None = None):
        self.rate = rate_per_min / 60.0
        self.capacity = capacity if capacity is not None else rate_per_min
        self.tokens = float(self.capacity)
        self.last = time.time()

    def allow(self, n: int = 1) -> bool:
        now = time.time()
        elapsed = now - self.last
        self.tokens = min(self.capacity, self.tokens + elapsed * self.rate)
        self.last = now
        if self.tokens >= n:
            self.tokens -= n
            return True
        return False
