from dataclasses import dataclass, field
from enum import Enum, auto
from math import exp
from typing import Dict, Set, List, Tuple
import time


# ---------- Config ----------

class Severity(Enum):
    NORMAL = 0
    OBSERVED = 1      # new pre-escalation buffer
    MINOR = 2
    MODERATE = 3
    CRITICAL = 4

@dataclass
class EscalationConfig:
    # Windows (seconds)
    WFAST_60: int = 60
    WFAST_120: int = 120
    WSLOW_60MIN: int = 60 * 60

    # Absolute thresholds (tune)
    BURST_MINOR_60: int = 5         # >= 5 in 60s
    BURST_MINOR_120: int = 10       # >=10 in 120s
    BURST_MODERATE_120: int = 20    # >=20 in 120s
    BURST_CRITICAL_120: int = 80    # >=80 in 120s (catastrophic/bypass)

    ACCUM_MINOR_60M: int = 20
    ACCUM_MODERATE_60M: int = 60
    ACCUM_CRITICAL_60M: int = 150

    # Step-down conditions
    T_STABLE: int = 4 * 60          # require 3–5 min; using 4m default
    RESTORE_RATIO: float = 0.80
    T_FLOOR: int = 2 * 60           # confidence below tier floor for 2m

    # Tick buffer (the “oh it’s back” guard)
    TICK_DEBOUNCE: int = 4          # wait N ticks before escalating beyond OBSERVED

    # Confidence model parameters
    K1: float = 0.15   # logistic slope for count
    M1: float = 10.0   # logistic midpoint for count
    K2: float = 12.0   # logistic slope for rate
    M2: float = 0.5    # logistic midpoint for rate (ONUs/sec ~ 30/min)
    K3: float = 0.12   # logistic slope for regional signal
    M3: float = 40.0   # logistic midpoint for regional signal
    TAU: float = 6 * 60.0  # freshness decay (seconds)

    # Confidence tier floors for gating transitions
    FLOOR_MINOR: float = 60.0
    FLOOR_MODERATE: float = 75.0
    FLOOR_CRITICAL: float = 90.0


# ---------- Contexts ----------

@dataclass
class OltCtx:
    olt_id: str
    # Current state & confidence
    state: Severity = Severity.NORMAL
    confidence: float = 0.0
    # Affected ONUs
    affected: Set[str] = field(default_factory=set)
    # Ring buffers (timestamps) for drop events to compute windows
    drops_60s: List[float] = field(default_factory=list)
    drops_120s: List[float] = field(default_factory=list)
    drops_60m: List[float] = field(default_factory=list)
    # Restoration tracking
    total_during_incident: int = 0
    restored_during_incident: int = 0
    # Timing
    last_transition_ts: float = 0.0
    last_activity_ts: float = 0.0
    # Tick-buffer
    observed_ts: float = 0.0
    tick_buffer: int = 0
    # Confidence floors
    below_floor_since: float = 0.0

@dataclass
class GlobalCtx:
    # Minimal structure for multi-OLT overlap
    # store recent per-OLT burst magnitudes with timestamps
    recent_bursts: List[Tuple[str, float, int]] = field(default_factory=list)  # (olt_id, ts, drop_count)


# ---------- Helpers ----------

def _now() -> float:
    return time.time()

def _logistic(x: float, k: float, m: float) -> float:
    return 100.0 * (1.0 / (1.0 + exp(-k * (x - m))))

def _prune_older_than(buf: List[float], cutoff: float) -> None:
    while buf and buf[0] < cutoff:
        buf.pop(0)

def _push(buf: List[float], ts: float) -> None:
    buf.append(ts)


# ---------- Event ingestion ----------

def on_offline_event(olt: OltCtx, onu_id: str, ts: float, cfg: EscalationConfig) -> None:
    if onu_id in olt.affected:
        return
    olt.affected.add(onu_id)
    olt.total_during_incident += 1
    olt.last_activity_ts = ts

    _push(olt.drops_60s, ts)
    _push(olt.drops_120s, ts)
    _push(olt.drops_60m, ts)

    # If we were totally normal, start OBSERVED & tick buffer
    if olt.state == Severity.NORMAL:
        olt.state = Severity.OBSERVED
        olt.observed_ts = ts
        olt.tick_buffer = 0
        olt.below_floor_since = 0.0

def on_online_event(olt: OltCtx, onu_id: str, ts: float) -> None:
    if onu_id not in olt.affected:
        return
    olt.affected.remove(onu_id)
    olt.restored_during_incident += 1
    olt.last_activity_ts = ts


# ---------- Confidence ----------

def compute_confidence(olt: OltCtx, global_ctx: GlobalCtx, now_ts: float, cfg: EscalationConfig) -> Tuple[float, Dict[str, float]]:
    # prune windows
    _prune_older_than(olt.drops_60s, now_ts - cfg.WFAST_60)
    _prune_older_than(olt.drops_120s, now_ts - cfg.WFAST_120)
    _prune_older_than(olt.drops_60m, now_ts - cfg.WSLOW_60MIN)

    # local burst magnitudes
    d60 = len(olt.drops_60s)
    d120 = len(olt.drops_120s)
    d60m = len(olt.drops_60m)

    # rate (choose the hotter window)
    window = cfg.WFAST_60 if d60 >= d120 * (cfg.WFAST_60/cfg.WFAST_120) else cfg.WFAST_120
    dn = max(d60, d120)
    rate = dn / max(1.0, float(window))

    # count & rate logits
    c_count = _logistic(dn, cfg.K1, cfg.M1)
    c_rate  = _logistic(rate, cfg.K2, cfg.M2)
    c_local_raw = 0.5 * c_count + 0.5 * c_rate
    c_isolated = min(100.0, float(dn))  # simple “big local spike speaks loudly”

    # freshness decay
    dt = max(0.0, now_ts - (olt.last_activity_ts or now_ts))
    c_fresh = olt.confidence * exp(-dt / cfg.TAU) if olt.confidence > 0 else 0.0

    # regional signal
    regional_signal = 0
    # consider bursts in the last ~2 minutes with ≥30s overlap window
    horizon = 120
    overlap = 30
    recent = [b for b in global_ctx.recent_bursts if now_ts - b[1] <= horizon]
    for oid, ts, drops in recent:
        if oid == olt.olt_id:
            continue
        if abs(now_ts - ts) <= overlap:
            regional_signal += drops
    c_regional = _logistic(regional_signal, cfg.K3, cfg.M3) if regional_signal > 0 else 0.0

    c_local = max(c_isolated, c_local_raw, c_fresh)
    c = max(0.0, min(100.0, 0.7 * c_local + 0.3 * c_regional))

    debug = {
        "d60": d60, "d120": d120, "d60m": d60m,
        "rate": rate,
        "c_count": c_count, "c_rate": c_rate,
        "c_local_raw": c_local_raw, "c_isolated": c_isolated,
        "c_fresh": c_fresh, "c_regional": c_regional,
        "c_final": c
    }
    return c, debug


# ---------- State machine + tick buffer ----------

def next_state(olt: OltCtx, now_ts: float, cfg: EscalationConfig) -> Severity:
    # convenience
    d60 = len(olt.drops_60s)
    d120 = len(olt.drops_120s)
    d60m = len(olt.drops_60m)
    dn = max(d60, d120)

    # thresholds
    burst_minor = (d60 >= cfg.BURST_MINOR_60) or (d120 >= cfg.BURST_MINOR_120)
    burst_moderate = (d120 >= cfg.BURST_MODERATE_120)
    burst_critical = (d120 >= cfg.BURST_CRITICAL_120)
    accum_minor = (d60m >= cfg.ACCUM_MINOR_60M)
    accum_moderate = (d60m >= cfg.ACCUM_MODERATE_60M)
    accum_critical = (d60m >= cfg.ACCUM_CRITICAL_60M)

    # tick buffer gating (except catastrophic bypass)
    can_escalate = True
    if olt.state in (Severity.OBSERVED, Severity.MINOR, Severity.MODERATE):
        if not burst_critical:  # catastrophic bypass if True
            can_escalate = (olt.tick_buffer >= cfg.TICK_DEBOUNCE)

    # Step-up logic with confidence floors
    if can_escalate:
        if olt.state == Severity.NORMAL:
            if burst_minor or accum_minor or olt.confidence >= cfg.FLOOR_MINOR:
                return Severity.MINOR
        elif olt.state == Severity.OBSERVED:
            if burst_critical or accum_critical or olt.confidence >= cfg.FLOOR_CRITICAL:
                return Severity.CRITICAL
            if burst_moderate or accum_moderate or olt.confidence >= cfg.FLOOR_MODERATE:
                return Severity.MODERATE
            if burst_minor or accum_minor or olt.confidence >= cfg.FLOOR_MINOR:
                return Severity.MINOR
        elif olt.state == Severity.MINOR:
            if burst_critical or accum_critical or olt.confidence >= cfg.FLOOR_CRITICAL:
                return Severity.CRITICAL
            if burst_moderate or accum_moderate or olt.confidence >= cfg.FLOOR_MODERATE:
                return Severity.MODERATE
        elif olt.state == Severity.MODERATE:
            if burst_critical or accum_critical or olt.confidence >= cfg.FLOOR_CRITICAL:
                return Severity.CRITICAL

    # Step-down logic (stability + restore ratio + floor)
    if olt.state in (Severity.CRITICAL, Severity.MODERATE, Severity.MINOR, Severity.OBSERVED):
        stable = (now_ts - (olt.last_activity_ts or now_ts)) >= cfg.T_STABLE
        restored_ratio = (olt.restored_during_incident / olt.total_during_incident) if olt.total_during_incident else 0.0

        # track if below floor long enough
        floor_map = {
            Severity.CRITICAL: cfg.FLOOR_CRITICAL,
            Severity.MODERATE: cfg.FLOOR_MODERATE,
            Severity.MINOR: cfg.FLOOR_MINOR,
            Severity.OBSERVED: cfg.FLOOR_MINOR * 0.9,  # small nudge
        }
        floor = floor_map.get(olt.state, 0.0)
        if olt.confidence < floor:
            if olt.below_floor_since == 0.0:
                olt.below_floor_since = now_ts
        else:
            olt.below_floor_since = 0.0

        below_floor_long_enough = (olt.below_floor_since and (now_ts - olt.below_floor_since) >= cfg.T_FLOOR)

        if stable and below_floor_long_enough and restored_ratio >= cfg.RESTORE_RATIO:
            # step down one tier at a time
            if olt.state == Severity.CRITICAL:
                return Severity.MODERATE
            if olt.state == Severity.MODERATE:
                return Severity.MINOR
            if olt.state == Severity.MINOR:
                return Severity.OBSERVED
            if olt.state == Severity.OBSERVED:
                return Severity.NORMAL

    return olt.state


# ---------- Cluster overlay ----------

@dataclass
class ClusterAssessment:
    regional_state: str  # "NONE", "WATCH", "CRITICAL"
    c_regional: float
    contributing_olts: List[str]

def cluster_assess(global_ctx: GlobalCtx, now_ts: float, cfg: EscalationConfig) -> ClusterAssessment:
    horizon = 300  # 5 minutes
    overlap = 30
    recent = [b for b in global_ctx.recent_bursts if now_ts - b[1] <= horizon]

    # group by near-simultaneity
    recent.sort(key=lambda x: x[1])
    if not recent:
        return ClusterAssessment("NONE", 0.0, [])

    # simple regional signal: sum drops where times are within ±overlap of the most recent burst
    pivot_ts = recent[-1][1]
    signal = sum(d for (_id, ts, d) in recent if abs(ts - pivot_ts) <= overlap)
    contributors = [oid for (oid, ts, d) in recent if abs(ts - pivot_ts) <= overlap]

    # logistic like before
    def _logistic_reg(x): return 100.0 * (1.0 / (1.0 + exp(-cfg.K3 * (x - cfg.M3))))
    c_regional = _logistic_reg(signal) if signal > 0 else 0.0

    if len(set(contributors)) >= 2 and c_regional >= 80:
        return ClusterAssessment("CRITICAL", c_regional, list(dict.fromkeys(contributors)))
    if c_regional >= 60:
        return ClusterAssessment("WATCH", c_regional, list(dict.fromkeys(contributors)))
    return ClusterAssessment("NONE", c_regional, list(dict.fromkeys(contributors)))


# ---------- Driver (call this per poll tick) ----------

def process_tick(olt: OltCtx, global_ctx: GlobalCtx, cfg: EscalationConfig, now_ts: float = None):
    now_ts = now_ts or _now()

    # Compute confidence
    c, _dbg = compute_confidence(olt, global_ctx, now_ts, cfg)
    olt.confidence = c

    # Tick buffer bookkeeping
    if olt.state == Severity.OBSERVED:
        olt.tick_buffer += 1

    # Catastrophic bursts get logged to global for cluster correlation
    d120 = len(olt.drops_120s)
    if d120 >= cfg.BURST_MINOR_120:
        global_ctx.recent_bursts.append((olt.olt_id, now_ts, d120))
        # keep list small
        global_ctx.recent_bursts = [b for b in global_ctx.recent_bursts if now_ts - b[1] <= 600]

    # Compute next state
    new_state = next_state(olt, now_ts, cfg)

    transitioned = (new_state != olt.state)
    if transitioned:
        olt.state = new_state
        olt.last_transition_ts = now_ts
        # reset counters when we go fully back to NORMAL
        if olt.state == Severity.NORMAL:
            olt.total_during_incident = 0
            olt.restored_during_incident = 0
            olt.tick_buffer = 0
            olt.observed_ts = 0.0
            olt.below_floor_since = 0.0

    return {
        "olt_id": olt.olt_id,
        "state": olt.state.name,
        "confidence": round(olt.confidence, 1),
        "affected": len(olt.affected),
        "transitioned": transitioned,
        "since_transition_s": int(now_ts - (olt.last_transition_ts or now_ts)),
    }
