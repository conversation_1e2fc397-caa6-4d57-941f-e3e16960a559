#!/usr/bin/env python3
from __future__ import annotations
import os, sys, types, argparse, time
from typing import Dict, List, Iterable

# --
# ensure TEST_MODE=1 for chatty logs, use the test cases below:
#
# TEST_MODE=1 python smoke_harness.py burst_one_olt --ticks 120 --tick-sec 1
# TEST_MODE=1 python smoke_harness.py trickle_one_olt --ticks 3600 --tick-sec 1
# TEST_MODE=1 python smoke_harness.py multi_olt_cut --ticks 180 --tick-sec 1
# TEST_MODE=1 python smoke_harness.py flap_guard --ticks 30 --tick-sec 1
#

# Make sure TEST_MODE is on before importing tasks (for logging gating)
os.environ.setdefault("TEST_MODE", "1")

# Import the app under test
import tasks  # must be importable from CWD (repo root)

# ---- Simulated time ----------------------------------------------------------
class SimClock:
    def __init__(self, start: float, step: float):
        self.t0 = float(start)
        self.step = float(step)
        self.tick = 0

    @property
    def now(self) -> float:
        return self.t0 + self.tick * self.step

    def advance(self, n: int = 1) -> None:
        self.tick += int(n)

# Patch tasks.time.time to our simulated clock
def _patch_time(clock: SimClock):
    tasks.time.time = lambda: clock.now  # type: ignore[attr-defined]

# ---- Scenario model ----------------------------------------------------------
Row = Dict[str, object]  # detector row schema

class Scenario:
    """
    Provides offline rows per region per tick.
    """
    def __init__(self, regions: List[str], olt_map: Dict[str, str]):
        self.regions = regions
        self.olt_map = olt_map
        self.offline_by_tick: Dict[int, Dict[str, List[str]]] = {}  # tick -> region -> [onu_id]
        self.directory: Dict[str, Row] = {}  # static per-ONU info for messages

    def set_offline(self, tick: int, region: str, onu_ids: Iterable[str]):
        self.offline_by_tick.setdefault(tick, {})
        self.offline_by_tick[tick][region] = list(onu_ids)

    def ensure_onu(self, onu_id: str, endpoint: str, client_id: int, addr: str, phone: str = "+15555550123", email: str = "<EMAIL>"):
        self.directory[onu_id] = {
            "onu_id": onu_id,
            "endpoint": endpoint,
            "client_id": client_id,
            "client_addr": addr,
            "phone": phone,
            "email": email,
            "sms_enabled": True,
            "email_enabled": True,
        }

    def detector_rows(self, tick: int, region: str) -> List[Row]:
        ons = self.offline_by_tick.get(tick, {}).get(region, [])
        rows: List[Row] = []
        for onu in ons:
            base = self.directory.get(onu)
            if not base:
                # fall back to a generic contact if not pre-registered
                base = {
                    "onu_id": onu,
                    "endpoint": onu,
                    "client_id": 0,
                    "client_addr": "-",
                    "phone": "+15555550123",
                    "email": "<EMAIL>",
                    "sms_enabled": True,
                    "email_enabled": True,
                }
            rows.append(dict(base))
        return rows

# ---- Scenarios ---------------------------------------------------------------
def make_scenario(name: str) -> Scenario:
    """
    Prebaked scenarios:
      - burst_one_olt: 80 ONUs drop in 80s on WTN (Critical/bypass), restore at t=300.
      - trickle_one_olt: 20 ONUs trickle every 180s across an hour on WTN (minor/observed).
      - multi_olt_cut: ~30 ONUs on WTN and ~35 on WL within 60s (regional WATCH/CRITICAL).
      - flap_guard: single ONU flaps down/up within a few ticks (should not escalate past OBSERVED).
    """
    regions = ["wtn", "wl", "off", "ccp", "wts"]
    # ONU->OLT naming convention for harness: prefix before '-' is OLT id
    olt_map = {}
    sc = Scenario(regions, olt_map)

    def reg_onus(prefix: str, count: int, start_index: int = 0, region: str = "wtn", addr_city="Somewhere"):
        for i in range(start_index, start_index + count):
            onu = f"{prefix}-{i:04d}"
            sc.ensure_onu(onu, endpoint=f"{prefix}-Cust-{i:04d}", client_id=10_000 + i, addr=f"{i} Main St, {addr_city}")
            olt_map[onu] = prefix
        return [f"{prefix}-{i:04d}" for i in range(start_index, start_index + count)]

    if name == "burst_one_olt":
        onus = reg_onus("WTN", 120, region="wtn", addr_city="WTN City")
        # 80 ONUs down 1 per second for ticks 0..79
        for t in range(0, 80):
            sc.set_offline(t, "wtn", onus[: t + 1])
        # hold down until 300, then restore all (i.e., detector returns empty)
        for t in range(80, 300):
            sc.set_offline(t, "wtn", onus[:80])

    elif name == "trickle_one_olt":
        onus = reg_onus("WTN", 30, region="wtn", addr_city="WTN City")
        # one new ONU offline every 180 seconds (ticks if tick-sec=1)
        offline_set: List[str] = []
        for t in range(0, 3600, 180):
            # grow the set by one ONU
            if len(offline_set) < 20:
                offline_set.append(onus[len(offline_set)])
            for h in range(t, min(t + 180, 3600)):
                sc.set_offline(h, "wtn", list(offline_set))

    elif name == "multi_olt_cut":
        wtn_onus = reg_onus("WTN", 100, region="wtn", addr_city="WTN City")
        wl_onus  = reg_onus("WL",  100, region="wl",  addr_city="WL City")
        # simultaneous surge over 60s
        for t in range(0, 60):
            sc.set_offline(t, "wtn", wtn_onus[: min(30, t + 1)])
            sc.set_offline(t, "wl",  wl_onus[: min(35, t + 1)])
        for t in range(60, 240):
            sc.set_offline(t, "wtn", wtn_onus[:30])
            sc.set_offline(t, "wl",  wl_onus[:35])

    elif name == "flap_guard":
        onus = reg_onus("WTN", 1, region="wtn", addr_city="WTN City")
        onu = onus[0]
        # tick 0: down; 1: up; 2: down; 3: up; should hover OBSERVED only
        sc.set_offline(0, "wtn", [onu])
        sc.set_offline(1, "wtn", [])      # restored
        sc.set_offline(2, "wtn", [onu])
        sc.set_offline(3, "wtn", [])      # restored
        for t in range(4, 30):
            sc.set_offline(t, "wtn", [])  # stable

    else:
        raise SystemExit(f"unknown scenario: {name}")

    return sc

# ---- Monkeypatch the app under test -----------------------------------------
def patch_app_for_sim(clock: SimClock, scenario: Scenario):
    # 1) Simulated time
    _patch_time(clock)

    # 2) Fake detector to feed rows per region/tick
    def fake_detect_offline_rows(region: str) -> List[Row]:
        return scenario.detector_rows(int(clock.tick), region)
    tasks.detect_offline_rows = fake_detect_offline_rows  # type: ignore

    # 3) Prevent real sends; print instead
    def fake_sms_raw(num: str, msg: str):
        print(f"[STAFF/CUST SMS] {num}: {msg}")
    def fake_email(to: List[str] | tuple | list, subject: str, body: str):
        print(f"[EMAIL] to={to} subj={subject} body_first_line={body.splitlines()[0] if body else ''}")
    tasks.send_sms_raw = fake_sms_raw       # type: ignore
    tasks.send_email = fake_email           # type: ignore

    # 4) Keep tickets “local” – just announce
    def fake_open_ticket(**kw):
        print(f"[TICKET OPEN] {kw.get('incident_key')} client={kw.get('client_id')} endpoint={kw.get('endpoint_name')}")
    def fake_close_ticket(**kw):
        print(f"[TICKET CLOSE] {kw.get('incident_key')} at {int(clock.now)}")
    tasks.tickets.open_minor_ticket = lambda **kw: fake_open_ticket(**kw)      # type: ignore
    tasks.tickets.close_minor_ticket = lambda **kw: fake_close_ticket(**kw)    # type: ignore

    # 5) Deterministic ONU->OLT mapping based on prefix (WTN-xxxx -> OLT WTN, WL-xxxx -> OLT WL)
    def fake_olt_id_for_onu(onu_id: str) -> str:
        return onu_id.split("-", 1)[0] if "-" in onu_id else "unknown"
    tasks._olt_id_for_onu = fake_olt_id_for_onu  # type: ignore

    # 6) Support list (so staff pages get “sent”)
    os.environ.setdefault("SUPPORT_SMS", "+15555550001,+15555550002")

# ---- CLI + main loop ---------------------------------------------------------
def main():
    ap = argparse.ArgumentParser(description="Gandalf escalation harness (drives tasks.poll_and_notify with simulated time)")
    ap.add_argument("scenario", choices=["burst_one_olt", "trickle_one_olt", "multi_olt_cut", "flap_guard"])
    ap.add_argument("--ticks", type=int, default=120, help="number of ticks to simulate (default: 120)")
    ap.add_argument("--tick-sec", type=float, default=1.0, help="seconds per tick (simulation) (default: 1.0)")
    ap.add_argument("--start-epoch", type=float, default=1_700_000_000.0, help="start epoch seconds (default: 1700000000)")
    args = ap.parse_args()

    clock = SimClock(start=args.start_epoch, step=args.tick_sec)
    scenario = make_scenario(args.scenario)
    patch_app_for_sim(clock, scenario)

    # Run loop
    print(f"=== RUN {args.scenario} ticks={args.ticks} step={args.tick_sec}s ===")
    for _ in range(args.ticks):
        # one poll
        report = tasks.poll_and_notify.apply().get() if hasattr(tasks.poll_and_notify, "apply") else tasks.poll_and_notify()
        # summarize OLT states each tick
        states = report.get("olts") if isinstance(report, dict) else {}
        print(f"[TICK {clock.tick:04d}] states={states}")
        # advance time
        clock.advance(1)

    print("=== DONE ===")

if __name__ == "__main__":
    main()
