# ticketing.py
from __future__ import annotations

import logging
import sqlite3
import time
from dataclasses import dataclass
from pathlib import Path
from typing import Optional
from datetime import datetime
from zoneinfo import ZoneInfo
from messaging import Messaging


import requests

log = logging.getLogger("ticketing")


# ---- time helpers (America/New_York) ----
_NY = ZoneInfo("America/New_York")


def _ny_iso(ts: int | float) -> str:
    """Return 'YYYY-MM-DDTHH:MM:SS-0400' (NY local offset) for CRM createdAt."""
    return datetime.fromtimestamp(int(ts), tz=_NY).strftime("%Y-%m-%dT%H:%M:%S%z")


def _ny_human(ts: int | float) -> str:
    """Human-friendly NY local time for email/SMS/comment text."""
    return datetime.fromtimestamp(int(ts), tz=_NY).strftime("%Y-%m-%d %H:%M:%S %Z")

# --- outage string helpers (NY local TZ) ---

def human_duration_from(started_ts: int, end_ts: int | None = None) -> str:
    """Return duration like '2h 3m', '1d 2h', '45s' from start→end (now if end is None)."""
    end = int(end_ts) if end_ts is not None else int(time.time())
    total = max(0, end - int(started_ts))

    d, rem = divmod(total, 86400)
    h, rem = divmod(rem, 3600)
    m, s = divmod(rem, 60)

    parts = []
    if d: parts.append(f"{d}d")
    if h: parts.append(f"{h}h")
    if m: parts.append(f"{m}m")
    if s or not parts: parts.append(f"{s}s")
    return " ".join(parts)


def staff_sms_line(name: str, address: str, started_ts: int, end_ts: int | None = None) -> str:
    """
    Example: 'John Smith — 102 Lake Dr, South Mills, 27976 down since 2025-08-24 14:07:03 EDT (2h 3m)'
    """
    addr = (address or "-").strip()
    return f"{name} — {addr} down since {_ny_human(started_ts)} ({human_duration_from(started_ts, end_ts)})"

class TicketStore:
    """
    Minimal local sqlite store to keep idempotency and CRM ids.
    """

    def __init__(self, path: str):
        self.path = Path(path)
        if self.path.parent and not self.path.parent.exists():
            self.path.parent.mkdir(parents=True, exist_ok=True)
        log.info("TicketStore at %s", self.path)
        self._init_db()

    def _connect(self):
        # More robust under occasional contention
        return sqlite3.connect(str(self.path), timeout=30)

    def _init_db(self) -> None:
        with self._connect() as db:
            db.execute(
                """
                CREATE TABLE IF NOT EXISTS tickets (
                    id             INTEGER PRIMARY KEY AUTOINCREMENT,
                    incident_key   TEXT UNIQUE,
                    client_id      TEXT,
                    endpoint_name  TEXT,
                    onu_id         TEXT,
                    address        TEXT,
                    status         TEXT,    -- open|closed
                    started_ts     INTEGER, -- epoch seconds
                    closed_ts      INTEGER, -- epoch seconds or NULL
                    last_update_ts INTEGER, -- epoch seconds or NULL
                    crm_ticket_id  TEXT,
                    created_at     INTEGER  -- epoch seconds
                )
                """
            )
            self._ensure_columns(
                [
                    ("incident_key", "TEXT"),
                    ("client_id", "TEXT"),
                    ("endpoint_name", "TEXT"),
                    ("onu_id", "TEXT"),
                    ("address", "TEXT"),
                    ("status", "TEXT"),
                    ("started_ts", "INTEGER"),
                    ("closed_ts", "INTEGER"),
                    ("last_update_ts", "INTEGER"),
                    ("crm_ticket_id", "TEXT"),
                    ("created_at", "INTEGER"),
                ]
            )
            db.commit()

    def _ensure_columns(self, cols: list[tuple[str, str]]) -> None:
        with self._connect() as db:
            cur = db.execute("PRAGMA table_info('tickets')")
            existing = {row[1] for row in cur.fetchall()}
            for name, typ in cols:
                if name not in existing:
                    db.execute(f"ALTER TABLE tickets ADD COLUMN {name} {typ}")
            db.commit()

    # --- helpers ---
    def get(self, incident_key: str) -> Optional[dict]:
        with self._connect() as db:
            cur = db.execute(
                """
                SELECT incident_key, client_id, endpoint_name, onu_id, address,
                       started_ts, crm_ticket_id, status, last_update_ts, created_at, closed_ts
                FROM tickets
                WHERE incident_key=?
                """,
                (incident_key,),
            )
            row = cur.fetchone()
            if not row:
                return None
            keys = [
                "incident_key",
                "client_id",
                "endpoint_name",
                "onu_id",
                "address",
                "started_ts",
                "crm_ticket_id",
                "status",
                "last_update_ts",
                "created_at",
                "closed_ts",
            ]
            return dict(zip(keys, row))

    def upsert_open(
        self,
        incident_key: str,
        *,
        client_id: str,
        endpoint_name: str,
        onu_id: str,
        started_ts: int,
        address: str,
    ) -> None:
        now_ts = int(time.time())
        with self._connect() as db:
            existing = db.execute(
                "SELECT incident_key FROM tickets WHERE incident_key=?", (incident_key,)
            ).fetchone()
            if existing:
                db.execute(
                    """
                    UPDATE tickets
                       SET client_id=?,
                           endpoint_name=?,
                           onu_id=?,
                           address=?,
                           started_ts=?,
                           status=?,
                           last_update_ts=?
                     WHERE incident_key=?
                    """,
                    (str(client_id), endpoint_name, onu_id, address, int(started_ts), "open", now_ts, incident_key),
                )
            else:
                db.execute(
                    """
                    INSERT INTO tickets
                        (incident_key, client_id, endpoint_name, onu_id, address,
                         status, started_ts, closed_ts, last_update_ts, crm_ticket_id, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, NULL, ?, NULL, ?)
                    """,
                    (incident_key, str(client_id), endpoint_name, onu_id, address, "open", int(started_ts), now_ts, now_ts),
                )
            db.commit()

    def set_crm_id(self, incident_key: str, crm_ticket_id: int | str) -> None:
        with self._connect() as db:
            cur = db.execute(
                "UPDATE tickets SET crm_ticket_id=?, last_update_ts=? WHERE incident_key=?",
                (str(crm_ticket_id), int(time.time()), incident_key),
            )
            if cur.rowcount == 0:
                log.warning("ticket: set_crm_id skipped (no row for %s)", incident_key)
            db.commit()

    def mark_closed(self, incident_key: str, *, closed_ts: int) -> None:
        with self._connect() as db:
            cur = db.execute(
                "UPDATE tickets SET status=?, closed_ts=?, last_update_ts=? WHERE incident_key=?",
                ("closed", int(closed_ts), int(time.time()), incident_key),
            )
            if cur.rowcount == 0:
                log.warning("ticket: mark_closed skipped (no row for %s)", incident_key)
            db.commit()

@dataclass
class TicketManager:
    """
    CRM wrapper + local store. Accepts either:
      TicketManager(store, uisp_client)   # uses client.crm_base / client.crm_token
      TicketManager(store, crm_base, crm_token)
    """
    store: TicketStore
    crm_base: str
    crm_token: str

    def __init__(self, store: TicketStore, client_or_base, token: str | None = None):
        self.store = store
        self._msg = Messaging(test_mode=False, brand="ESVC")  # or expose a flag if you want
        if hasattr(client_or_base, "crm_base") and hasattr(client_or_base, "crm_token"):
            self.crm_base = (client_or_base.crm_base or "").rstrip("/")
            self.crm_token = str(getattr(client_or_base, "crm_token", "") or "")
        else:
            self.crm_base = (str(client_or_base) if client_or_base else "").rstrip("/")
            self.crm_token = str(token or "")

    # -------- internal HTTP --------
    def _crm(self, method: str, path: str, **kwargs):
        if not self.crm_base or not self.crm_token:
            raise RuntimeError("CRM base/token missing")
        url = f"{self.crm_base}/{path.lstrip('/')}"
        headers = kwargs.pop("headers", {})
        headers["x-auth-token"] = self.crm_token
        timeout = kwargs.pop("timeout", 20)
        resp = requests.request(method, url, headers=headers, timeout=timeout, **kwargs)
        if resp.status_code >= 400:
            raise RuntimeError(f"{resp.status_code}: {resp.text}")
        ct = resp.headers.get("Content-Type", "")
        return resp.json() if "application/json" in ct else resp.text

    # -------- external API for tasks.py --------
    def open_minor_ticket(
        self,
        *,
        incident_key: str,
        client_id: str | int,
        endpoint_name: str,
        onu_id: str,
        started_ts: int,
        address: str,
        extra_note: str | None = None,
        public: bool = True,
    ) -> int | None:
        """
        Idempotent open:
          - upsert local row
          - if a CRM ticket is already associated and still open, DO NOT create another
          - otherwise create the CRM ticket and persist its id
        """
        # Always ensure local row exists/updated
        self.store.upsert_open(
            incident_key,
            client_id=str(client_id),
            endpoint_name=endpoint_name,
            onu_id=onu_id,
            started_ts=int(started_ts),
            address=address or "-",
        )

        # If we already have an open CRM ticket for this incident, skip creating a new one
        row = self.store.get(incident_key) or {}
        existing_crm_id = (row.get("crm_ticket_id") or "").strip()
        status = (row.get("status") or "open").strip().lower()
        if existing_crm_id and status == "open":
            log.info(
                "ticket: already open (CRM #%s) for %s; skipping create",
                existing_crm_id,
                endpoint_name,
            )
            try:
                return int(existing_crm_id)
            except Exception:
                return None

        if not (self.crm_base and self.crm_token and str(client_id).strip()):
            log.info("ticket: local-only (CRM base/token or client_id missing) for %s", endpoint_name)
            return None

        created_at_iso = _ny_iso(started_ts)
        body = self._msg.ticket_open_comment(endpoint_name, onu_id, address, started_ts, extra_note)

        if extra_note:
            body += f"\n{extra_note}"

        payload = {
            "subject": f"[ESVC] Minor outage — {endpoint_name}",
            "clientId": int(str(client_id)),
            "status": 0,            # new
            "public": bool(public), # visible to client
            "activity": [
                {
                    "userId": None,
                    "createdAt": created_at_iso,  # NY local with offset
                    "public": True,
                    "comment": {"body": body},
                }
            ],
        }

        try:
            data = self._crm("POST", "/ticketing/tickets", json=payload)
            if isinstance(data, dict) and "id" in data:
                self.store.set_crm_id(incident_key, data["id"])
                log.info("ticket: opened CRM #%s for %s", data["id"], endpoint_name)
                return int(data["id"])
            log.info("ticket: CRM create returned unexpected response: %r", data)
            return None
        except Exception as e:
            log.warning("ticket: CRM create failed for %s (%s): %s", endpoint_name, incident_key, e)
            return None

    def close_minor_ticket(
        self,
        *,
        incident_key: str,
        closed_ts: int,
        note: str | None = None,
        public: bool = True,
    ) -> None:
        """
        Close an existing ticket in CRM:
          - PATCH /ticketing/tickets/{id} with status=3 (resolved/closed)
          - include clientId when available
          - keep public=True
          - add an activity comment with restoration message and NY-local time
        Only mark local closed after CRM confirms (or if there is no CRM id).
        """
        row = self.store.get(incident_key)
        if not row:
            log.warning("ticket: close skipped, no local row for incident %s", incident_key)
            return

        crm_id = (row or {}).get("crm_ticket_id")
        client_id = (row or {}).get("client_id")
        endpoint = (row or {}).get("endpoint_name") or "(unknown)"

        if not crm_id or not (self.crm_base and self.crm_token):
            log.info("ticket: local close (no CRM id) for incident %s", incident_key)
            self.store.mark_closed(incident_key, closed_ts=closed_ts)
            return

        closed_at_iso = _ny_iso(closed_ts)
        # closed_human = _ny_human(closed_ts)
        subject = f"Service restored — {endpoint}"
        start_ts = int((row or {}).get("started_ts") or closed_ts)
        total = human_duration_from(start_ts, closed_ts)
        # comment_body = note or f"Service restored at {closed_human}. Total duration: {total}."
        comment_body = self._msg.ticket_close_comment(start_ts, closed_ts, f"Total Duration: {total}")

        payload = {
            "status": 3,                 # resolved/closed
            "public": bool(public),
            "subject": subject,
            "activity": [
                {
                    "userId": None,
                    "createdAt": closed_at_iso,
                    "public": True,
                    "comment": {"body": comment_body},
                }
            ],
        }
        if client_id:
            try:
                payload["clientId"] = int(str(client_id))
            except Exception:
                pass

        # Try full payload, then minimal (some stacks can be picky)
        try:
            self._crm("PATCH", f"/ticketing/tickets/{crm_id}", json=payload)
            log.info("ticket: closed CRM #%s", crm_id)
            self.store.mark_closed(incident_key, closed_ts=closed_ts)
        except Exception as e1:
            try:
                minimal = {"status": 3}
                if client_id:
                    minimal["clientId"] = int(str(client_id))
                self._crm("PATCH", f"/ticketing/tickets/{crm_id}", json=minimal)
                log.info("ticket: closed CRM #%s (second try, minimal payload)", crm_id)
                self.store.mark_closed(incident_key, closed_ts=closed_ts)
            except Exception as e2:
                log.warning("ticket: CRM close failed for #%s: %s / %s", crm_id, e1, e2)
                # Leave local row open so the next loop can retry.
