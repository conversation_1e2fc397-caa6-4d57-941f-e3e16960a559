# uisp_client.py
import os
import json
import requests
from typing import Any, Dict, Optional
from config import settings

class UispError(Exception):
    pass

class UispClient:
    """
    Minimal UISP client for the MVP detector.
    Uses a single 'NSM' base (controller), auth via x-auth-token.
    """

    def __init__(
            self,
            base_url: str,
            token: str,
            *,
            crm_base: str = "",
            crm_token: str = "",
            timeout: float = 15.0,
    ):
        self.base_url = base_url.rstrip("/")
        self.token = token
        self.crm_base = crm_base.rstrip("/") if crm_base else ""
        self.crm_token = crm_token
        self.timeout = timeout

        if not self.base_url or not self.token:
            raise UispError("Missing UISP_BASE_URL or UISP_TOKEN")

    # --- core HTTP (overwrite both) ---
    def _nsm(self, method: str, path: str, **kwargs):
        import requests
        url = f"{self.base_url.rstrip('/')}{path}"
        headers = {"x-auth-token": self.token, "Content-Type": "application/json"}
        timeout = kwargs.pop("timeout", self.timeout)
        resp = requests.request(method, url, headers=headers, timeout=timeout, **kwargs)
        if resp.status_code >= 400:
            raise UispError(f"{resp.status_code}: {resp.text}")
        return resp.json() if resp.text else None

    def _crm(self, method: str, path: str, **kwargs):
        import requests
        if not self.crm_base or not self.crm_token:
            raise UispError("CRM base/token not configured")
        url = f"{self.crm_base.rstrip('/')}{path}"
        headers = {"x-auth-token": self.crm_token, "Content-Type": "application/json"}
        timeout = kwargs.pop("timeout", self.timeout)
        resp = requests.request(method, url, headers=headers, timeout=timeout, **kwargs)
        if resp.status_code >= 400:
            raise UispError(f"{resp.status_code}: {resp.text}")
        return resp.json() if resp.text else None

    # ---- Helpers ----
    @staticmethod
    def _yn(v) -> bool:
        if v is None:
            return False
        if isinstance(v, str):
            s = v.strip().lower()
            if s in {"yes","y","true","t","1"}: return True
            if s in {"no","n","false","f","0"}: return False
        return bool(v)

    # --- NSM helpers ---
    def get_endpoint_crm_client_id(self, endpoint_site_id: str) -> int | None:
        """
        Resolve the CRM client ID bound to an NSM endpoint/site.
        Tries the common fields defensively and returns int.
        """
        s = self.nsm_get_site(endpoint_site_id) or {}

        # try likely locations (your sample shows both of these):
        for path in [
            ("description", "ucrmId"),  # string like "135"
            ("ucrm", "client", "id"),  # number or string
            ("identification", "ucrmId"),  # seen on some installs
            ("ucrmId",),  # legacy
        ]:
            cur = s
            ok = True
            for p in path:
                if not isinstance(cur, dict) or p not in cur:
                    ok = False
                    break
                cur = cur[p]
            if ok and cur is not None:
                try:
                    return int(cur)
                except Exception:
                    try:
                        return int(str(cur).strip())
                    except Exception:
                        pass
        return None

    # --- overwrite these three helpers ---
    def nsm_get_device(self, device_id: str) -> dict:
        return self._nsm("GET", f"/devices/{device_id}") or {}

    def nsm_get_site(self, site_id: str) -> dict:
        # Try /sites/{id}, then /sites?id=<id> as fallback
        try:
            return self._nsm("GET", f"/sites/{site_id}") or {}
        except UispError:
            data = self._nsm("GET", "/sites", params={"id": str(site_id)})
            if isinstance(data, list):
                for s in data:
                    if str(s.get("id")) == str(site_id):
                        return s
                return data[0] if data else {}
            return data or {}

    def map_device_to_client_id(self, device_id: str) -> int | None:
        dev = self.nsm_get_device(device_id)
        site = (dev.get("identification") or {}).get("site") or {}
        site_id = site.get("id")
        if not site_id:
            return None
        s = self.nsm_get_site(site_id)
        # Prefer description.ucrmId
        cid = ((s.get("description") or {}).get("ucrmId") or "").strip()
        if cid.isdigit():
            return int(cid)
        # Fallback: ucrm.client.id
        cli = (s.get("ucrm") or {}).get("client") or {}
        if cli.get("id") is not None:
            try:
                return int(cli["id"])
            except Exception:
                return None
        return None

    def client_id_from_device(self, onu_id: str) -> tuple[int | None, str | None, str | None]:
        """
        Resolve (client_id, endpoint_site_id, endpoint_name) for a given ONU using device→site→ucrm.
        Returns (client_id|None, endpoint_site_id|None, endpoint_name|None).
        """
        dev = self.nsm_get_device(onu_id) or {}
        ident = dev.get("identification") or {}
        site = ident.get("site") or {}
        ep_site_id = site.get("id")
        ep_name = site.get("name")

        client_id = None
        if ep_site_id:
            s = self.nsm_get_site(ep_site_id) or {}
            ucrm = s.get("ucrm") or {}
            cli = ucrm.get("client") or {}
            if cli.get("id") is not None:
                try:
                    client_id = int(cli["id"])
                except Exception:
                    # Sometimes strings leak through; best effort
                    client_id = int(str(cli["id"]))

        return client_id, (str(ep_site_id) if ep_site_id else None), (str(ep_name) if ep_name else None)

    # --- New: read yes/no style custom attribute flags from CRM client payload ---
    def crm_flag_from_client(self, client_obj: dict, key: str, default: bool = False) -> bool:
        """
        Read a yes/no style custom attribute (e.g. 'sms_notify', 'email_notify')
        from a CRM client's payload. Falls back to `default` if not present.
        """

        def _yn(v):
            if v is None:
                return default
            if isinstance(v, str):
                s = v.strip().lower()
                if s in {"yes", "y", "true", "t", "1"}: return True
                if s in {"no", "n", "false", "f", "0"}: return False
            return bool(v)

        attrs = (client_obj or {}).get("attributes") or []
        key_l = (key or "").strip().lower()
        for a in attrs:
            if (a.get("key") or a.get("name") or "").strip().lower() == key_l:
                return _yn(a.get("value"))
        return bool(default)

    def crm_get_client(self, client_id: int | str) -> dict:
        """Return CRM client object as dict."""
        return self._crm("GET", f"/clients/{int(client_id)}") or {}

    def crm_primary_contact(self, client_id: int | str) -> dict:
        """
        Return primary contact info + notify flags for a CRM client.
        {
          "email": str|None,
          "phone": str|None (E.164),
          "sms_enabled": bool,
          "email_enabled": bool
        }
        """
        c = self.crm_get_client(client_id) or {}

        # Flags from custom attributes if present (sms_notify / email_notify)
        sms_enabled = None
        email_enabled = None
        for a in (c.get("attributes") or []):
            key = (a.get("key") or a.get("name") or "").strip().lower()
            val = a.get("value")
            if key in {"sms_notify", "sms-notify", "smsnotify"}:
                sms_enabled = self._yn(val)
            elif key in {"email_notify", "email-notify", "emailnotify"}:
                email_enabled = self._yn(val)

        # Choose a primary contact (prefers contact/billing)
        contacts = c.get("contacts") or []
        primary = None
        for ct in contacts:
            if ct.get("isContact") or ct.get("isBilling"):
                primary = ct
                break
        if not primary and contacts:
            primary = contacts[0]

        # Email fallback chain (contact → client)
        email = None
        if primary and (primary.get("email") or "").strip():
            email = primary["email"].strip()
        else:
            # Some deployments keep a login username that is an email
            if (c.get("username") or "").strip() and "@" in c["username"]:
                email = c["username"].strip()
            elif (c.get("email") or "").strip():
                email = c["email"].strip()

        # Phone from contact only (avoid random client-level strings)
        phone = self._normalize_phone(primary.get("phone") if primary else None)

        # Default flags if not explicitly set:
        #  - If not specified, allow the channel if we actually have a reachable address
        if sms_enabled is None:
            sms_enabled = bool(phone)
        if email_enabled is None:
            email_enabled = bool(email)

        return {
            "email": email or None,
            "phone": phone,
            "sms_enabled": bool(sms_enabled),
            "email_enabled": bool(email_enabled),
        }

    def crm_update_ticket(self, ticket_id: int, payload: dict) -> dict:
        return self._crm("PATCH", f"/ticketing/tickets/{int(ticket_id)}", json=payload) or {}

    def crm_create_ticket(self, payload: dict) -> dict:
        """
        Compatibility shim used by smoke_test.py:
        POST /ticketing/tickets with a fully-formed payload, return CRM response dict.
        """
        return self._crm("POST", "/ticketing/tickets", json=payload) or {}

    def crm_close_ticket(self, ticket_id: int, note: str | None = None) -> dict:
        """
        Mark a CRM ticket as closed by updating its status.
        Optionally attach a private comment (activity).
        """
        ticket_id = int(ticket_id)

        payload = {"status": 2}  # 0=open, 1=pending, 2=resolved/closed (per UISP CRM)
        if note:
            payload["activity"] = [{
                "public": False,
                "comment": {"body": note}
            }]

        return self._crm("PUT", f"/ticketing/tickets/{ticket_id}", json=payload)

    def crm_search_clients(self, query: str, limit: int = 5) -> Any:
        # GET /clients?query=...&limit=...
        return self._crm("GET", "/clients", params={"query": query, "limit": limit})

    def crm_get_client_contacts(self, client_id: int | str) -> Any:
        # GET /clients/{id}/contacts
        return self._crm("GET", f"/clients/{client_id}/contacts")

    # One-shot: look up a contact by endpoint display name (no caching)
    def crm_lookup_contact_by_name(self, endpoint_name: str) -> dict | None:
        """
        Best-effort lookup by client name using /clients?query=
        Returns:
          {
            "client_id": int,
            "email": str|None,
            "phone": str|None,
            "sms_enabled": bool,
            "email_enabled": bool,
            "client": { full CRM client object }   # for address fallback
          }
        or None if not found.
        """
        q = (endpoint_name or "").strip()
        if len(q) < 2:
            return None

        try:
            data = self._crm("GET", "/clients", params={"query": q, "limit": 10})
        except Exception as e:
            # Let caller decide; we log at call site in tasks
            return None

        if not isinstance(data, list) or not data:
            return None

        # heuristic: exact match on "Last, First" or company
        def _score(c: dict) -> int:
            last = (c.get("lastName") or "").strip().lower()
            first = (c.get("firstName") or "").strip().lower()
            company = (c.get("companyName") or "").strip().lower()
            target = q.lower()
            score = 0
            if last and first and f"{last}, {first}" == target:
                score += 100
            if company and company == target:
                score += 90
            # partials
            if last and last in target:
                score += 10
            if first and first in target:
                score += 10
            if company and company in target:
                score += 10
            return score

        best = sorted(data, key=_score, reverse=True)[0]

        # pull primary contact (first email/phone we find)
        email = None
        phone = None
        for ct in best.get("contacts", []) or []:
            if not email:
                e = (ct.get("email") or "").strip()
                if e:
                    email = e
            if not phone:
                p = (ct.get("phone") or "").strip()
                if p:
                    phone = p
            if email and phone:
                break

        # flags: explicit “no” turns off, otherwise default to True
        def _yn_default_yes(v):
            if v is None:
                return True
            if isinstance(v, str):
                s = v.strip().lower()
                if s in {"no", "n", "false", "0"}:
                    return False
                if s in {"yes", "y", "true", "1"}:
                    return True
            return bool(v)

        # try to locate custom attributes ‘sms_notify’ and ‘email_notify’
        attr_map = {}
        for a in best.get("attributes", []) or []:
            key = (a.get("key") or "").strip()
            if key:
                attr_map[key] = a.get("value")

        sms_enabled = _yn_default_yes(attr_map.get("sms_notify"))
        email_enabled = _yn_default_yes(attr_map.get("email_notify"))

        # include entire client for address fallback
        return {
            "client_id": best.get("id"),
            "email": email,
            "phone": phone,
            "sms_enabled": sms_enabled,
            "email_enabled": email_enabled,
            "client": best,
        }

    def list_crm_clients(self) -> Any:
        """
        GET /clients from CRM. Your earlier code used `'/clients'`, so we'll do that too.
        """
        return self._crm("GET", "/clients")

    # ---- Sites ----
    def list_sites(self, *, type_: Optional[str] = None, ucrm_details: bool = False) -> Any:
        """
        GET /sites with optional filters.
        type_ in {"site","endpoint","client","subscriber"}
        """
        params: Dict[str, Any] = {}
        if type_:
            params["type"] = type_
        if ucrm_details:
            params["ucrmDetails"] = True
        return self._nsm("GET", "/sites", params=params)

    # --- CRM helpers for custom attributes --------------------------------------

    def crm_list_custom_attributes(self):
        """GET /clients/custom-attributes  -> list of {id, name, key, type, ...}"""
        return self._crm("GET", "/clients/custom-attributes")

    def _as_list(self, obj):
        if isinstance(obj, list):
            return obj
        if isinstance(obj, dict):
            return obj.get("items") or obj.get("data") or []
        return []

    def _find_custom_attribute_def(self, key: str) -> dict | None:
        """Find a custom attribute definition by its 'key'."""
        defs = self._as_list(self.crm_list_custom_attributes())
        key_l = (key or "").strip().lower()
        for d in defs:
            if (d.get("key") or "").strip().lower() == key_l:
                return d
        return None

    def _client_attribute_map(self, client: dict) -> dict[str, dict]:
        """
        Return {key -> attribute_row} from the client's embedded attributes array.
        attribute_row typically has: { id, clientId, customAttributeId, key, value, ... }
        """
        m = {}
        for a in self._as_list(client.get("attributes") or []):
            k = (a.get("key") or "").strip().lower()
            if k:
                m[k] = a
        return m

    def _bool_to_attr_value(self, desired: bool, existing_value: str | None) -> str:
        """
        Convert True/False to the string the CRM expects.
        If existing value looked numeric '0'/'1', keep numeric; otherwise use 'yes'/'no'.
        """
        if isinstance(existing_value, str) and existing_value.strip().lower() in {"0", "1"}:
            return "1" if desired else "0"
        if isinstance(existing_value, str) and existing_value.strip().lower() in {"yes", "no"}:
            return "yes" if desired else "no"
        # default to numeric
        return "1" if desired else "0"

    def crm_set_client_attribute_by_key(self, client_id: int | str, key: str, value_bool: bool) -> dict:
        """
        Upsert client custom attribute by key (e.g., 'sms_notify', 'email_notify').
        - If attribute exists for client -> PATCH/PUT /clients/{id}/attributes/{attrId}
        - Else look up definition -> POST /clients/{id}/attributes with customAttributeId
        Returns the updated attribute row (or raises on error).
        """
        key_norm = (key or "").strip().lower()
        client = self.crm_get_client(client_id) or {}
        attr_map = self._client_attribute_map(client)

        existing = attr_map.get(key_norm)
        if existing:
            # Update existing attribute value
            new_value = self._bool_to_attr_value(value_bool, existing.get("value"))
            # prefer PATCH, fallback to PUT if needed by your CRM
            try:
                return self._crm("PATCH", f"/clients/{client_id}/attributes/{existing['id']}",
                                 json={"value": new_value})
            except Exception:
                return self._crm("PUT", f"/clients/{client_id}/attributes/{existing['id']}",
                                 json={"value": new_value})

        # Not present -> need definition to create it
        attr_def = self._find_custom_attribute_def(key_norm)
        if not attr_def:
            raise RuntimeError(f"Custom attribute definition not found for key={key_norm!r}")

        new_value = self._bool_to_attr_value(value_bool, None)
        # Create new attribute row for this client
        created = self._crm("POST", f"/clients/{client_id}/attributes", json={
            "customAttributeId": attr_def["id"],
            "value": new_value
        })
        return created

    # --- Public toggler ----------------------------------------------------------

    def crm_toggle_notify(self, client_id: int | str, sms: bool | None = None, email: bool | None = None) -> dict:
        """
        Toggle client-level notification preferences stored as custom attributes.
          - sms=True/False -> sets 'sms_notify'
          - email=True/False -> sets 'email_notify'
        Returns a summary of what changed.
        """
        result = {"client_id": client_id, "updated": {}}

        if sms is not None:
            row = self.crm_set_client_attribute_by_key(client_id, "sms_notify", bool(sms))
            result["updated"]["sms_notify"] = row.get("value")

        if email is not None:
            row = self.crm_set_client_attribute_by_key(client_id, "email_notify", bool(email))
            result["updated"]["email_notify"] = row.get("value")

        return result

    # ---- Devices ----
    def get_devices_by_site(self, site_id: str, dev_type: Optional[str] = None) -> Any:
        params: Dict[str, Any] = {"siteId": site_id}
        if dev_type:
            params["type"] = dev_type  # e.g. "onu"
        return self._nsm("GET", "/devices", params=params)

    def get_onus_by_site(self, site_id: str) -> Any:
        # Preferred path to fetch ONUs directly scoped to one site
        return self._nsm("GET", "/devices/onus", params={"siteId": site_id})

    def get_onus_by_parent(self, olt_id: str) -> Any:
        # Correct endpoint (with parentId query), per Swagger.
        return self._nsm("GET", "/devices/onus", params={"parentId": olt_id})

    def get_site_with_ucrm_details(self, site_id: str):
        """Fetch a single site with UCRM details included."""
        data = self._nsm("GET", "/sites", params={"id": site_id, "ucrmDetails": True})
        # Normalize result: /sites?id=... may return a list
        if isinstance(data, list):
            for s in data:
                if str(s.get("id")) == str(site_id):
                    return s
            return data[0] if data else {}
        # Some servers return an object with "items"
        if isinstance(data, dict) and isinstance(data.get("items"), list):
            for s in data["items"]:
                if str(s.get("id")) == str(site_id):
                    return s
            return data["items"][0] if data["items"] else {}
        return data or {}

def get_onu_context(self, onu_id: str) -> dict:
    """
    Enriched context for an ONU. Never raises; returns '-' or 'unknown' when missing.
    Keys: REGION, OLT_ID, OLT_NAME, ONU_ID, ONU_NAME, CLIENT_ID, CLIENT_NAME,
          CLIENT_STREET, CLIENT_CITY, CLIENT_ZIP, CLIENT_EMAIL, CLIENT_PHONE,
          PREF_SMS_NOTIFY, PREF_EMAIL_NOTIFY
    """
    def _g(d, *p, default=None):
        cur = d
        for x in p:
            if isinstance(cur, dict) and x in cur:
                cur = cur[x]
            else:
                return default
        return cur

    def _first(*vals):
        for v in vals:
            if isinstance(v, str) and v.strip():
                return v.strip()
        return None

    # Device
    try:
        dev = self.nsm_get_device(onu_id) or {}
    except Exception:
        dev = {}
    onu_name = _g(dev, "identification", "name") or dev.get("name") or "-"

    # Parent (OLT)
    olt_id = (
        _g(dev, "attributes", "parentId")
        or _g(dev, "identification", "parent", "id")
        or _g(dev, "overview", "parentId")
    )
    olt_name = "-"
    if olt_id:
        try:
            od = self.nsm_get_device(str(olt_id)) or {}
            olt_name = _g(od, "identification", "name") or str(olt_id)
        except Exception:
            olt_name = str(olt_id)
    else:
        olt_id, olt_name = "unknown", "UNKNOWN OLT"

    # Site
    site_id = (
        _g(dev, "identification", "site", "id")
        or _g(dev, "identification", "siteId")
        or _g(dev, "overview", "siteId")
        or _g(dev, "overview", "site", "id")
        or _g(dev, "site", "id")
    )
    try:
        site = self.nsm_get_site(site_id) if site_id else {}
    except Exception:
        site = {}

    # Region (config first, then heuristics)
    region = "UNKNOWN"
    try:
        locs = getattr(settings, "LOCATIONS", {}) or {}
        site_name = _g(site, "identification", "name", default="") or site.get("name", "")
        U = (olt_name or "").upper() + " " + (site_name or "").upper()
        # config exact match
        for rkey, meta in locs.items():
            R = str(rkey).upper()
            if R in U:
                region = R
                break
        # heuristic fallback
        if region == "UNKNOWN":
            for code in ("WTN", "WL", "CCP", "WTS", "OFF"):
                if code in U:
                    region = code
                    break
    except Exception:
        pass

    # CRM client
    crm_id = None
    try:
        ucrm = (site or {}).get("ucrm") or {}
        uc = ucrm.get("client") or {}
        if uc.get("id") is not None:
            crm_id = int(uc["id"])
    except Exception:
        crm_id = None

    try:
        crm = self.crm_get_client(crm_id) if crm_id else {}
    except Exception:
        crm = {}

    client_name = _first(
        crm.get("name"),
        " ".join(x for x in [crm.get("firstName"), crm.get("lastName")] if x),
    ) or "-"

    client_street = _first(crm.get("street1"), crm.get("street"))
    client_city = _first(crm.get("city"))
    client_zip = _first(crm.get("zipCode"), crm.get("zip"), crm.get("postalCode"))

    # contact
    email = _first(crm.get("email"))
    phone = _first(crm.get("phone"), crm.get("phoneNumber"))
    for c in crm.get("contacts") or []:
        if not email:
            email = _first(c.get("email"))
        if not phone:
            phone = _first(c.get("phone"), c.get("phoneNumber"))
        if email and phone:
            break

    # prefs (search deep)
    prefs = {}
    def walk(n):
        if isinstance(n, dict):
            for k, v in n.items():
                kl = str(k).lower()
                if kl in ("sms_notify", "email_notify") and kl not in prefs and isinstance(v, (str, int, bool)):
                    prefs[kl] = str(v)
            key_name = str(n.get("key") or n.get("name") or n.get("code") or n.get("label") or "").lower()
            if key_name in ("sms_notify", "email_notify") and key_name not in prefs:
                val = n.get("value") or n.get("val") or n.get("stringValue")
                if val is not None:
                    prefs[key_name] = str(val)
            for v in n.values():
                walk(v)
        elif isinstance(n, (list, tuple)):
            for it in n:
                walk(it)
    walk(crm)

    return {
        "REGION": region,
        "ONU_ID": onu_id,
        "ONU_NAME": onu_name,
        "OLT_ID": str(olt_id),
        "OLT_NAME": str(olt_name),
        "CLIENT_ID": crm_id if crm_id is not None else "-",
        "CLIENT_NAME": client_name or "-",
        "CLIENT_STREET": client_street or "-",
        "CLIENT_CITY": client_city or "-",
        "CLIENT_ZIP": client_zip or "-",
        "CLIENT_EMAIL": (email or "-"),
        "CLIENT_PHONE": (phone or "-"),
        "PREF_SMS_NOTIFY": str(prefs.get("sms_notify", "unknown")).lower(),
        "PREF_EMAIL_NOTIFY": str(prefs.get("email_notify", "unknown")).lower(),
    }
