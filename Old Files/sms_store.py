# sms_store.py
import os, sqlite3, time
from typing import List


class SmsStore:
    def __init__(self, path: str):
        # Normalize path so it can be either a directory or a full file path
        p = (path or "").strip()
        if not p:
            p = os.path.join(os.getcwd(), "sms_store.sqlite3")
        # If path points to a directory (or looks like one), append default filename
        if p.endswith(("\\", "/")) or os.path.isdir(p):
            p = os.path.join(p.rstrip("\\/"), "sms_store.sqlite3")
        self.path = p
        os.makedirs(os.path.dirname(self.path) or ".", exist_ok=True)
        self._init()

    def _init(self):
        with sqlite3.connect(self.path) as db:
            db.execute("""CREATE TABLE IF NOT EXISTS sms_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ts REAL NOT NULL,
                recipient TEXT NOT NULL,
                key TEXT NOT NULL,
                body TEXT
            )""")
            db.execute("CREATE INDEX IF NOT EXISTS idx_sms_uni ON sms_log(recipient, key)")
            db.commit()

    def log_sms(self, recipient: str, key: str, body: str):
        with sqlite3.connect(self.path) as db:
            db.execute("INSERT INTO sms_log (ts, recipient, key, body) VALUES (?,?,?,?)",
                       (time.time(), recipient, key, body))
            db.commit()

    def recently_sent(self, recipient: str, key: str, within_secs: int) -> bool:
        cutoff = time.time() - within_secs
        with sqlite3.connect(self.path) as db:
            cur = db.execute(
                "SELECT 1 FROM sms_log WHERE recipient=? AND key=? AND ts>=? LIMIT 1",
                (recipient, key, cutoff),
            )
            return cur.fetchone() is not None

    def recipients_for_key(self, key: str, within_days: int = 7) -> List[str]:
        cutoff = time.time() - within_days * 86400
        with sqlite3.connect(self.path) as db:
            cur = db.execute(
                "SELECT DISTINCT recipient FROM sms_log WHERE key=? AND ts>=?",
                (key, cutoff),
            )
            return [r[0] for r in cur.fetchall()]

    def prune_older_than_days(self, days: int = 7):
        cutoff = time.time() - days * 86400
        with sqlite3.connect(self.path) as db:
            db.execute("DELETE FROM sms_log WHERE ts < ?", (cutoff,))
            db.commit()
