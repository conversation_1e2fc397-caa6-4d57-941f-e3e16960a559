# run_Local_loop.py
import os
import time
import logging
from config import settings
from tasks import poll_and_notify  # Celery-decorated task; we'll call .run()

def _ensure_store_dir():
    path = settings.SMS_STORE_PATH
    d = os.path.dirname(path)
    if d and not os.path.exists(d):
        os.makedirs(d, exist_ok=True)

def main():
    logging.basicConfig(level=logging.INFO, format="%(asctime)s %(levelname)s %(name)s: %(message)s")
    log = logging.getLogger("runner")

    if not settings.NSM_BASE_URL or not settings.NSM_TOKEN:
        log.error("Please set UISP_BASE_URL and UISP_TOKEN environment variables.")
        return

    _ensure_store_dir()

    interval = int(getattr(settings, "POLL_INTERVAL_SECS", 30))
    log.info("Starting standalone poller. Regions=%s, interval=%ss", settings.REGION_KEYS, interval)

    try:
        while True:
            try:
                # run the Celery task synchronously
                poll_and_notify.run()
            except Exception as e:
                log.exception("Poll failed: %s", e)
            time.sleep(interval)
    except KeyboardInterrupt:
        log.info("Shutting down. Bye!")

if __name__ == "__main__":
    main()
