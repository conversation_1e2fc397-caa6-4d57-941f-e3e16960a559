from __future__ import annotations
"""
tasks.py — Celery tasks for probe/notify + daily digest
=======================================================

What this file does
-------------------
1) Runs a *single* "probe & notify" tick (safe for Celery beat).
2) Builds and sends one consolidated **Daily Network Digest** email:
   - Outage roll-ups (from daily JSONL journal)
   - Per-OLT 24h traffic and optics (DOM RX) graphs (embedded inline)
   - Cluster summary with FS3900 sanity notes
   - Retention / maintenance actions (pruned files, DB vacuum, etc.)
3) Moves legacy maintenance/retention emails into that digest (less noise).
4) Keeps all human-facing text consistent via MessageBuilder.

Design & conventions
--------------------
- We import and *use* MessageBuilder from probe_and_notify.py for tone/phrasing.
- Graphs are embedded in the HTML body as data URIs (no attachments needed).
- We don’t shell out; we call orchestrator code directly in-process if present.
- DB- or metrics-specific collectors you already have can stay; this file only
  *reads* their outputs and renders a nice email.

Developer notes
---------------
- If you rename paths in config, adjust SETTINGS near the top.
- If `FS3900_BY_OLT` is missing for an OLT, we silently skip that switch.
- This file won’t crash if some optional bits are missing; it degrades gracefully.
"""
# ─────────────────────────────
# Stdlib
# ─────────────────────────────
import os
import time
import json
import logging
import types
from typing import Dict, List, Tuple, Iterable, Optional
from contextlib import contextmanager
from datetime import datetime, timezone

try:
    from zoneinfo import ZoneInfo
except Exception:  # pragma: no cover
    ZoneInfo = None  # type: ignore

# ─────────────────────────────
# Third-party
# ─────────────────────────────
from celery.utils.log import get_task_logger

# Use your Celery instance namespace
from celery_app import app

# ─────────────────────────────
# Local modules (unchanged)
# ─────────────────────────────
from config import settings as SETTINGS
from fs3900_probe import FS3900Probe
from storage import (
    init_probe_db,
    persist_probe_rows,
    daily_summary,
    rollup_5m as db_rollup_5m,
    rollup_30m as db_rollup_30m,
    retention_prune as db_retention_prune,
)
import notify
from uisp_service import UISPService
from probe_and_notify import (
    Orchestrator,
    OutageState,
    STATE_PATH as PN_STATE_PATH,
    JOURNAL_PATH as PN_JOURNAL_PATH,
    LOCK_PATH as PN_LOCK_PATH,
    DEFAULT_INCLUDE_SUSPENDED,
)

LOG = get_task_logger(__name__)

# ─────────────────────────────
# Constants & Config glue
# ─────────────────────────────
LOCAL_TZ = ZoneInfo(SETTINGS.TIMEZONE) if ZoneInfo and getattr(SETTINGS, "TIMEZONE", None) else timezone.utc

DB_PATH = os.path.abspath(getattr(SETTINGS, "PROBE_DB_PATH", "device_probe.sqlite3"))
DAILY_STATUS_RECIPIENTS: List[str] = list(getattr(SETTINGS, "DAILY_STATUS_RECIPIENTS", []) or [])

TEST_MODE = int(getattr(SETTINGS, "TEST_MODE", 0))
TEST_EMAIL_TO: str = getattr(SETTINGS, "TEST_EMAIL_TO", "") or ""
DRY_RUN_NOTIFICATIONS: bool = bool(getattr(SETTINGS, "DRY_RUN_NOTIFICATIONS", False))

# Use separate locks so FS and ONU probes don’t block each other unnecessarily
LOCK_DIR = os.path.abspath(os.path.dirname(PN_LOCK_PATH or "./.state/probe.lock") or ".")
os.makedirs(LOCK_DIR, exist_ok=True)
LOCK_FS  = os.path.join(LOCK_DIR, "switch_probe.lock")
LOCK_ONU = os.path.join(LOCK_DIR, "onu_probe.lock")

# TTL ≈ 4× poll interval, minimum 5 minutes
# Replace the single LOCK_TTL_S with these two lines:
LOCK_TTL_FS_S  = max(int(getattr(SETTINGS, "POLL_INTERVAL_SECS", 30)) * 2, 120)
LOCK_TTL_ONU_S = max(int(getattr(SETTINGS, "POLL_INTERVAL_SECS", 30)) * 2, 120)

STATE_PATH   = PN_STATE_PATH or "./.state/probe.json"
JOURNAL_PATH = PN_JOURNAL_PATH or "./daily_outages.jsonl"

# ─────────────────────────────
# Utilities
# ─────────────────────────────
def _now_local() -> datetime:
    """Return aware datetime in configured timezone. Output: datetime (tz-aware)."""
    return datetime.now(LOCAL_TZ)

def _resolve_regions() -> List[str]:
    """
    Determine which regions to poll.
    Input: none (config/env)
    Output: list[str] region keys (lowercased)
    """
    raw = str(getattr(SETTINGS, "REGION_KEYS", "") or "").strip()
    if raw:
        return [r.strip().lower() for r in raw.split(",") if r.strip()]
    locs = getattr(SETTINGS, "LOCATIONS", {}) or {}
    return sorted([str(k).lower() for k in locs.keys()])

def _pid_is_alive(pid: int) -> bool:
    try:
        if pid <= 0:
            return False
        os.kill(pid, 0)  # signal 0 == probe existence
        return True
    except ProcessLookupError:
        return False
    except PermissionError:
        # Process exists but may be owned by another user; consider alive
        return True
    except Exception:
        return False

def _read_lockinfo(path: str) -> Tuple[Optional[int], Optional[float]]:
    pid = None
    age = None
    try:
        with open(path, "r", encoding="utf-8") as f:
            txt = f.read().strip()
        # Accept either "PID" or "PID TIMESTAMP"
        parts = txt.split()
        if parts:
            try:
                pid = int(parts[0])
            except Exception:
                pid = None
    except Exception:
        pid = None
    try:
        st = os.stat(path)
        age = max(0.0, time.time() - st.st_mtime)
    except Exception:
        age = None
    return pid, age

@contextmanager
def _single_run_lock(path: str, ttl_s: int) -> Iterable[bool]:
    """
    Best-effort host-local lock with PID+TTL reaping.
    Writes "<pid> <epoch>" into the lock on acquire.
    """
    os.makedirs(os.path.dirname(path) or ".", exist_ok=True)
    acquired = False
    try:
        # If an old lock exists, decide whether to reap it
        if os.path.exists(path):
            pid, age = _read_lockinfo(path)
            if age is not None and age > max(ttl_s, 60):
                try:
                    os.remove(path)
                    LOG.warning("lock: reaped stale lock %s (age=%.1fs > ttl=%ss)", path, age, ttl_s)
                except Exception:
                    pass
            else:
                # If owner PID is gone, reap even if not past TTL (after 30s grace)
                if pid and not _pid_is_alive(pid):
                    if (age or 0) > 30:
                        try:
                            os.remove(path)
                            LOG.warning("lock: reaped orphan lock %s (pid=%s dead, age=%.1fs)", path, pid, age or -1)
                        except Exception:
                            pass

        # Try to acquire
        fd = os.open(path, os.O_CREAT | os.O_EXCL | os.O_WRONLY, 0o644)
        try:
            os.write(fd, f"{os.getpid()} {int(time.time())}\n".encode("utf-8"))
        finally:
            os.close(fd)
        acquired = True
        yield True
    except FileExistsError:
        pid, age = _read_lockinfo(path)
        LOG.info("lock busy: %s (owner_pid=%s, age=%ss, ttl=%ss)", path, pid, int(age or -1), ttl_s)
        yield False
    except Exception:
        LOG.exception("lock: unexpected error on %s", path)
        yield False
    finally:
        if acquired:
            try:
                os.remove(path)
            except Exception:
                # If we crash after this point, TTL/owner reaping will clean it next run
                pass


# ─────────────────────────────
# FS3900 → rows transformer
# ─────────────────────────────
def _portnum_from_ifname(ifname: str) -> Optional[int]:
    """
    Parse 'ethernet 1/26' → 26
    Input: ifname (str)
    Output: port number (int) or None
    """
    if not ifname:
        return None
    import re
    m = re.search(r"\b1\s*/\s*(\d+)\b", ifname, re.I)
    return int(m.group(1)) if m else None

def _fs_snapshot_to_rows(*, snapshot: Dict, olt_id: str, device_name: str, olt_ip: str, fs_ip: str, ifname: str) -> List[Dict]:
    """
    Convert FS3900Probe.snapshot(ports=[...]) into candidate rows for ALL scanned ports.
    Emits booleans for fs_up/olt_up/has_traffic (so storage._b() persists 1/0),
    and a summary that includes switch identity for quick reading.
    """
    ports = list(snapshot.get("ports") or [])
    rows: List[Dict] = []
    for p in ports:
        port = p.get("port")
        dom  = p.get("dom") or {}
        traf = p.get("traffic") or {}
        oper = (p.get("oper_status") or "").lower() or None
        worst = p.get("worst_severity") or None

        in_bps  = float(traf.get("in_bps") or 0.0)
        out_bps = float(traf.get("out_bps") or 0.0)
        has_traf = (in_bps > 0.0 or out_bps > 0.0)  # BOOL
        fs_link_up = (oper == "up")                 # BOOL
        # We treat OLT “up” as the uplink being up on the switch; true signal of
        # the PON chassis isn’t visible via the switch alone.
        olt_link_up = fs_link_up                    # BOOL

        this_if = f"ethernet 1/{port}" if isinstance(port, int) else (ifname or "")

        row = {
            "olt_id":      olt_id,
            "device_name": device_name,
            "olt_ip":      olt_ip,
            "fs_ip":       fs_ip,
            "fs_ifname":   this_if,
            "fs_status":   "up" if fs_link_up else (oper or "unknown"),
            "fs_up":       fs_link_up,     # BOOL -> storage._b -> 1/0
            "olt_up":      olt_link_up,    # BOOL -> storage._b -> 1/0
            "in_bps":      in_bps,
            "out_bps":     out_bps,
            "has_traffic": has_traf,       # BOOL -> storage._b -> 1/0
            "dt_ms":       traf.get("dt_ms"),
            "dom_rx_dbm":  (None if dom.get("rx_dbm") is None else float(dom["rx_dbm"])),
            "dom_tx_dbm":  (None if dom.get("tx_dbm") is None else float(dom["tx_dbm"])),
            "dom_temp_c":  (None if dom.get("temp_c") is None else float(dom["temp_c"])),
            "dom_vcc_v":   (None if dom.get("vcc_v") is None else float(dom["vcc_v"])),
            "dom_bias_ma": (None if dom.get("bias_ma") is None else float(dom["bias_ma"])),
            "dom_switch":  worst,
            "error":       None,
            "summary":     f"{device_name} [{fs_ip}] {this_if}: "
                           f"oper={oper or 'n/a'} rx={dom.get('rx_dbm','n/a')} dBm "
                           f"in={in_bps:.0f} out={out_bps:.0f} bps",
        }
        rows.append(row)
    return rows



# ─────────────────────────────
# Email builder shim (uses your notify.*)
# ─────────────────────────────
def _compute_sfp_rollup(summary: Dict, top_n: int = 12) -> Dict:
    """
    Extract and roll up SFP/DOM issues from the summary into:
      summary["sfp_rollup"] = {
          "totals": {"alarm": N, "warning": N, "caution": N},
          "top_offenders": [{"device": "...", "iface": "...", "count": n, "worst_rx_dbm": -xx.x}, ...]
      }
    Non-destructive: we don't delete original details; the builder can choose to ignore them.
    """
    # Find any lists of dicts that look like DOM/SFP rows (we look for 'dom_switch' or 'dom_rx_dbm')
    def _collect_rows(obj):
        out = []
        if isinstance(obj, dict):
            for v in obj.values():
                out.extend(_collect_rows(v))
        elif isinstance(obj, list):
            for it in obj:
                if isinstance(it, dict) and ("dom_switch" in it or "dom_rx_dbm" in it):
                    out.append(it)
                else:
                    out.extend(_collect_rows(it))
        return out

    rows = _collect_rows(summary)
    totals = {"alarm": 0, "warning": 0, "caution": 0}
    offenders = {}

    for r in rows:
        sev = str(r.get("dom_switch", "")).lower()
        if sev in totals:
            totals[sev] += 1
        # Identity of the port/device
        dev = r.get("device_name") or r.get("fs_ip") or r.get("host") or "unknown-device"
        iface = r.get("fs_uplink_ifname") or r.get("ifname") or r.get("if") or r.get("port") or "iface?"
        key = f"{dev} | {iface}"
        ent = offenders.setdefault(key, {"device": dev, "iface": iface, "count": 0, "worst_rx_dbm": None})
        ent["count"] += 1
        try:
            rx = float(r.get("dom_rx_dbm")) if r.get("dom_rx_dbm") is not None else None
        except Exception:
            rx = None
        if rx is not None:
            if ent["worst_rx_dbm"] is None or rx < ent["worst_rx_dbm"]:
                ent["worst_rx_dbm"] = rx

    top = sorted(offenders.values(), key=lambda x: (-x["count"], (x["worst_rx_dbm"] if x["worst_rx_dbm"] is not None else 0)))[:top_n]
    summary["sfp_rollup"] = {"totals": totals, "top_offenders": top}
    return summary


def _send_daily_status_email(summary: Dict) -> Tuple[str, str]:
    """
    Build + send daily digest using notify.MessageBuilder/ChartBuilder if present.
    Input:  summary (dict from storage.daily_summary)
    Output: (subject, recipients_hint)
    """
    subject = ""
    body_text = ""
    body_html = None

    # 1) Roll up SFP/DOM noise up-front
    try:
        summary = _compute_sfp_rollup(summary, top_n=12)
    except Exception:
        LOG.exception("daily_status_digest: SFP rollup failed; continuing")

    MB = getattr(notify, "MessageBuilder", None)
    CB = getattr(notify, "ChartBuilder", None)

    # 2) Prefer rich message builder if it exists
    if callable(getattr(MB, "build_daily_digest_email", None)):
        try:
            subject, body_text, body_html = MB.build_daily_digest_email(
                summary=summary,
                tz_name=getattr(SETTINGS, "TIMEZONE", "America/New_York"),
            )
        except Exception:
            LOG.exception("MessageBuilder failed; falling back to text-only")
            MB = None

    # 3) Inline charts (base64 <img>): try TOTAL In/Out first, then per-OLT
    import base64, time as _t
    since_ts = int(_t.time()) - 24 * 3600
    charts_html_parts = []

    # 3a) Global In/Out if ChartBuilder provides it
    try:
        if body_html and CB and callable(getattr(CB, "render_total_traffic_charts", None)):
            total_figs = CB.render_total_traffic_charts(db_path=DB_PATH, since_ts=since_ts)
            for name, data in (total_figs or []):
                charts_html_parts.append(
                    f"<div><p style='margin:8px 0 2px'>{name}</p>"
                    f"<img src='data:image/png;base64,{base64.b64encode(data).decode('ascii')}' "
                    f"style='max-width:900px;width:100%;height:auto'/></div>"
                )
    except Exception:
        LOG.exception("ChartBuilder total traffic charts failed; continuing")

    # 3b) Existing per-OLT charts (already in your file)
    try:
        if body_html and CB and callable(getattr(CB, "render_olt_charts", None)):
            figs = CB.render_olt_charts(db_path=DB_PATH, since_ts=since_ts, limit_olts=8)
            for name, data in (figs or []):
                charts_html_parts.append(
                    f"<div><p style='margin:8px 0 2px'>{name}</p>"
                    f"<img src='data:image/png;base64,{base64.b64encode(data).decode('ascii')}' "
                    f"style='max-width:900px;width:100%;height:auto'/></div>"
                )
    except Exception:
        LOG.exception("ChartBuilder per-OLT charts failed; continuing")

    if body_html and charts_html_parts:
        body_html += "\n<hr/>\n<h3>Network Traffic (last 24h)</h3>\n" + "\n".join(charts_html_parts)

    # 4) If we still don’t have a subject/body, build a minimal, *rolled-up* text fallback
    if not subject:
        # Don’t call compose_daily_email; it tends to list every SFP scream.
        roll = summary.get("sfp_rollup", {})
        tot = roll.get("totals", {})
        offenders = roll.get("top_offenders", [])
        lines = [
            "Daily Network Digest (24h)",
            "--------------------------",
            f"SFP totals: alarm={tot.get('alarm',0)} warning={tot.get('warning',0)} caution={tot.get('caution',0)}",
        ]
        if offenders:
            lines.append("Top SFP offenders:")
            for o in offenders:
                rx = f" rx={o['worst_rx_dbm']:.2f} dBm" if o.get("worst_rx_dbm") is not None else ""
                lines.append(f" - {o['device']} {o['iface']}: {o['count']} hits{rx}")
        body_text = "\n".join(lines)
        subject = "Daily Network Digest (rolled up)"
        body_html = None  # keep it simple on fallback

    # 5) Send
    recipients = DAILY_STATUS_RECIPIENTS[:] or []
    if TEST_MODE and not recipients and TEST_EMAIL_TO:
        recipients = [TEST_EMAIL_TO]
    if not recipients:
        LOG.info("daily_status_digest: no recipients configured; skipping send")
        return subject, "(no recipients)"

    try:
        notify.send_email(recipients, subject, body_text or "(empty)", body_html)
        return subject, ",".join(recipients)
    except Exception:
        LOG.exception("daily_status_digest: send_email failed")
        return subject, "send_email error"


# ─────────────────────────────
# Core builders
# ─────────────────────────────
def _build_orchestrator(*, dry_run: bool, debug: bool) -> Orchestrator:
    """
    Create a fully wired Orchestrator.
    Inputs:
      dry_run: bool — if True, suppress all notifications inside orchestrator
      debug:   bool — extra logging
    Output:
      Orchestrator instance (call orchestrate_once(regions=[...]))
    """
    svc = UISPService.from_config(debug=debug)
    state = OutageState(STATE_PATH)
    return Orchestrator(
        svc=svc,
        state=state,
        journal_path=JOURNAL_PATH,
        include_suspended=DEFAULT_INCLUDE_SUSPENDED,
        dry_run=dry_run,
        debug=debug,
        test_client=None,
        test_email=(TEST_EMAIL_TO if TEST_MODE else None),
        test_sms=None,
    )

# --- Deterministic switch probe (no guessing) ---------------------------------
def _run_switch_probe_once() -> dict:
    """
    Deterministic FS3900 sweep, optimized for correct bps:
      - One SNMP snapshot per switch for link/DOM/oper (no per-port sleeps).
      - One-sleep multi-port traffic measurement per switch (default 1.5s).
      - Only the ports declared in config.py (exact OLT uplinks + inter-switch links).
      - If a configured port isn't in the snapshot -> emit an error row (no guessing).
      - Guard: if dt_ms < FS_MIN_DT_MS, suppress bps for that row to avoid "teleport to zero".

    Tunables (env or Settings):
      FS_TRAFFIC_SLEEP_S  (float) default 1.5   # SNMP agent tick ~0.75s; 1.5s spans >=2 ticks
      FS_MIN_DT_MS        (int)   default 1200  # treat intervals below this as unreliable
    """
    import os, time, re
    from typing import Dict, Any, List, Optional, Tuple
    from fs3900_probe import FS3900Probe
    from storage import init_probe_db, persist_probe_rows

    # Resolve SETTINGS and logger
    try:
        SETTINGS  # type: ignore[name-defined]
    except NameError:  # pragma: no cover
        try:
            from config import settings as SETTINGS  # type: ignore
        except Exception:
            from config import Settings  # type: ignore
            SETTINGS = Settings()  # type: ignore

    try:
        LOG  # type: ignore[name-defined]
    except NameError:  # pragma: no cover
        from celery.utils.log import get_task_logger
        LOG = get_task_logger(__name__)  # type: ignore

    # ---------- helpers ----------
    def _portnum_from_ifname(ifname: str | None) -> Optional[int]:
        if not ifname:
            return None
        m = re.search(r"\bethernet\s+\d+\/(\d+)\b", str(ifname), re.IGNORECASE)
        if m:
            return int(m.group(1))
        m = re.search(r"\bport\s*([0-9]+)\b", str(ifname), re.IGNORECASE)
        if m:
            return int(m.group(1))
        tail = re.findall(r"([0-9]+)$", str(ifname).strip())
        return int(tail[0]) if tail else None

    def _row_from_port(ps: Dict[str, Any], *, olt_id: str, device_name: str,
                       olt_ip: str, fs_ip: str, ifname: str) -> Dict[str, Any]:
        traf = ps.get("traffic") or {}
        dom  = ps.get("dom") or {}
        oper = (ps.get("oper_status") or "unknown").lower()
        in_bps  = float(traf.get("in_bps") or 0.0) if traf is not None else 0.0
        out_bps = float(traf.get("out_bps") or 0.0) if traf is not None else 0.0
        has_trf = (in_bps > 0.0 or out_bps > 0.0)
        return {
            "olt_id": olt_id,
            "device_name": device_name,
            "olt_ip": olt_ip,
            "fs_ip": fs_ip,
            "fs_ifname": ifname,
            "fs_status": oper,
            "fs_up": (oper == "up"),
            "olt_up": (oper == "up"),
            "in_bps": in_bps,
            "out_bps": out_bps,
            "has_traffic": has_trf,
            "dt_ms": traf.get("dt_ms") if traf else None,
            "dom_rx_dbm": (None if dom.get("rx_dbm") is None else float(dom["rx_dbm"])),
            "dom_tx_dbm": (None if dom.get("tx_dbm") is None else float(dom["tx_dbm"])),
            "dom_temp_c": (None if dom.get("temp_c") is None else float(dom["temp_c"])),
            "dom_vcc_v":  (None if dom.get("vcc_v")  is None else float(dom["vcc_v"])),
            "dom_bias_ma":(None if dom.get("bias_ma")is None else float(dom["bias_ma"])),
            "dom_switch": ps.get("worst_severity"),
            "error": None,
            "summary": f"{device_name} [{fs_ip}] {ifname}: "
                       f"oper={oper} rx={(dom.get('rx_dbm','n/a'))} dBm "
                       f"in={in_bps:.0f} out={out_bps:.0f} bps",
        }

    def _err_row(*, olt_id: str, device_name: str, olt_ip: str, fs_ip: str,
                 ifname: str, why: str) -> Dict[str, Any]:
        return {
            "olt_id": olt_id,
            "device_name": device_name,
            "olt_ip": olt_ip,
            "fs_ip": fs_ip,
            "fs_ifname": ifname,
            "fs_status": "unknown",
            "fs_up": False,
            "olt_up": False,
            "in_bps": None,
            "out_bps": None,
            "has_traffic": None,
            "dt_ms": None,
            "dom_rx_dbm": None,
            "dom_tx_dbm": None,
            "dom_temp_c": None,
            "dom_vcc_v": None,
            "dom_bias_ma": None,
            "dom_switch": "unknown",
            "error": why,
            "summary": f"{device_name} [{fs_ip}] {ifname}: {why}",
        }

    t0 = time.time()
    now_ts = int(t0)

    # DB path
    db_path = os.path.abspath(
        getattr(SETTINGS, "PROBE_DB_PATH", None)
        or getattr(SETTINGS, "DB_PATH", None)
        or "device_probe.sqlite3"
    )
    try:
        init_probe_db(db_path)
    except Exception:
        LOG.exception("switch_probe: init db failed (db_path=%s)", db_path)

    # Tunables
    community = getattr(SETTINGS, "SNMP_COMMUNITY", None) or os.getenv("SNMP_COMMUNITY") or "public"
    sleep_s   = float(os.getenv("FS_TRAFFIC_SLEEP_S", str(getattr(SETTINGS, "FS_TRAFFIC_SLEEP_S", 1.5))))
    min_dt_ms = int(os.getenv("FS_MIN_DT_MS", "1500"))

    # Gather deterministic plan from settings
    olt_cfg  = dict(getattr(SETTINGS, "OLT_PROBES", {}) or {})
    link_cfg = list(getattr(SETTINGS, "FS_LINK_PROBES", []) or [])

    # plan: fs_ip -> {'ports': set[int], 'olts': [..], 'links':[...]}
    plan: Dict[str, Dict[str, Any]] = {}
    def _add_plan_port(fs_ip: str, port: Optional[int]):
        if port is None: return
        plan.setdefault(fs_ip, {"ports": set(), "olts": [], "links": []})
        plan[fs_ip]["ports"].add(int(port))

    # OLT exact ports
    for olt_id, meta in olt_cfg.items():
        fs_ip   = str(meta.get("fs_ip") or "").strip()
        ifname  = str(meta.get("fs_uplink_ifname") or "").strip()
        device  = str(meta.get("name") or olt_id)
        olt_ip  = str(meta.get("olt_ip") or "")
        portnum = _portnum_from_ifname(ifname)
        plan.setdefault(fs_ip or "__missing__", {"ports": set(), "olts": [], "links": []})
        plan.setdefault(fs_ip, {"ports": set(), "olts": [], "links": []})
        plan[fs_ip]["olts"].append({
            "olt_id": str(olt_id), "device_name": device, "olt_ip": olt_ip,
            "fs_ip": fs_ip, "ifname": ifname, "port": portnum
        })
        _add_plan_port(fs_ip, portnum)

    # LINK exact ports
    for lk in link_cfg:
        fs_ip   = str(lk.get("fs_ip") or "").strip()
        ifname  = str(lk.get("fs_ifname") or "").strip()
        name    = str(lk.get("name") or "").strip() or f"LINK {fs_ip} {ifname}"
        oid     = str(lk.get("olt_id") or "").strip() or f"link-{fs_ip.replace('.','_')}-{ifname}"
        portnum = _portnum_from_ifname(ifname)
        plan.setdefault(fs_ip or "__missing__", {"ports": set(), "olts": [], "links": []})
        plan.setdefault(fs_ip, {"ports": set(), "olts": [], "links": []})
        plan[fs_ip]["links"].append({
            "olt_id": oid, "device_name": name, "fs_ip": fs_ip,
            "ifname": ifname, "port": portnum
        })
        _add_plan_port(fs_ip, portnum)

    # Snapshots: one per fs_ip (DOM/oper only; we add bps next)
    snapshots: Dict[str, Dict[str, Any]] = {}
    probes: Dict[str, FS3900Probe] = {}
    for fs_ip, bucket in plan.items():
        if not fs_ip or fs_ip == "__missing__":
            continue
        ports = tuple(sorted(int(p) for p in (bucket.get("ports") or [])))
        try:
            probe = FS3900Probe(host=fs_ip, community=community, ports=ports)
            probes[fs_ip] = probe
            snapshots[fs_ip] = probe.snapshot(sample_traffic=False)  # no per-port sleeps
        except Exception:
            LOG.exception("switch_probe: snapshot failed for fs_ip=%s ports=%s", fs_ip, ports)
            snapshots[fs_ip] = {"host": fs_ip, "t": int(time.time()), "elapsed_s": 0.0, "ports": []}

    # Build a map: fs_ip -> {portnum -> port_snapshot_dict}
    def _portmap(fs_ip: str) -> Dict[int, Dict[str, Any]]:
        snap = snapshots.get(fs_ip) or {}
        out: Dict[int, Dict[str, Any]] = {}
        for ps in (snap.get("ports") or []):
            try:
                p = int(ps.get("port"))
            except Exception:
                continue
            out[p] = ps
        return out

    rows: List[Dict[str, Any]] = []
    # Track (row_index, fs_ip, ifindex) for bps writeback
    row_ifindexes: List[Tuple[int, str, int]] = []
    cnt_olts = 0
    cnt_links = 0

    for fs_ip, bucket in plan.items():
        pmap = _portmap(fs_ip)

        # OLTs
        for item in bucket["olts"]:
            cnt_olts += 1
            port = item.get("port")
            ifname = item["ifname"] or (f"ethernet 1/{port}" if port else "")
            ps = pmap.get(port) if port is not None else None
            if ps:
                r = _row_from_port(ps, olt_id=item["olt_id"], device_name=item["device_name"],
                                   olt_ip=item["olt_ip"], fs_ip=fs_ip, ifname=ifname)
                rows.append(r)
                ifidx = ps.get("ifindex")
                if isinstance(ifidx, int):
                    row_ifindexes.append((len(rows)-1, fs_ip, ifidx))
            else:
                rows.append(_err_row(olt_id=item["olt_id"], device_name=item["device_name"],
                                     olt_ip=item["olt_ip"], fs_ip=fs_ip, ifname=ifname,
                                     why="port_not_found"))

        # LINKs
        for lk in bucket["links"]:
            cnt_links += 1
            port = lk.get("port")
            ifname = lk["ifname"] or (f"ethernet 1/{port}" if port else "")
            ps = pmap.get(port) if port is not None else None
            if ps:
                r = _row_from_port(ps, olt_id=lk["olt_id"], device_name=lk["device_name"],
                                   olt_ip="", fs_ip=fs_ip, ifname=ifname)
                rows.append(r)
                ifidx = ps.get("ifindex")
                if isinstance(ifidx, int):
                    row_ifindexes.append((len(rows)-1, fs_ip, ifidx))
            else:
                rows.append(_err_row(olt_id=lk["olt_id"], device_name=lk["device_name"],
                                     olt_ip="", fs_ip=fs_ip, ifname=ifname,
                                     why="port_not_found"))

    # ---- One-sleep multi-port traffic per switch ----
    # Group requested ifindexes by fs_ip
    wanted: Dict[str, List[int]] = {}
    for _, ip, ifidx in row_ifindexes:
        wanted.setdefault(ip, []).append(ifidx)

    # Query each switch once with the standardized sleep_s
    for fs_ip, ifidxes in wanted.items():
        probe = probes.get(fs_ip)
        if not probe:
            continue
        try:
            results = probe.sample_bps_multi(ifidxes, sleep_s=sleep_s)  # {ifindex: (in_bps, out_bps, dt_ms)}
        except Exception:
            LOG.exception("switch_probe: sample_bps_multi failed for %s", fs_ip)
            results = {}

        # Index the row positions per ifindex for this switch
        idx_to_rows: Dict[int, List[int]] = {}
        for row_i, ip, ifidx in row_ifindexes:
            if ip == fs_ip:
                idx_to_rows.setdefault(ifidx, []).append(row_i)

        for ifidx, triple in results.items():
            ib, ob, dt = triple if isinstance(triple, tuple) and len(triple) == 3 else (None, None, None)
            for row_i in idx_to_rows.get(ifidx, []):
                # Guard: ignore too-short intervals to avoid fake zeros
                if not dt or (isinstance(dt, (int, float)) and dt < min_dt_ms):
                    rows[row_i]["dt_ms"] = int(dt or 0)
                    rows[row_i]["in_bps"] = None
                    rows[row_i]["out_bps"] = None
                    rows[row_i]["has_traffic"] = None
                    rows[row_i]["error"] = f"short_dt:{int(dt or 0)}ms"
                else:
                    rows[row_i]["dt_ms"] = int(dt)
                    rows[row_i]["in_bps"] = float(ib or 0.0)
                    rows[row_i]["out_bps"] = float(ob or 0.0)
                    rows[row_i]["has_traffic"] = bool((ib or 0.0) > 0.0 or (ob or 0.0) > 0.0)

                # Refresh summary with measured bps
                dev = rows[row_i].get("device_name") or rows[row_i].get("olt_id") or ""
                ip_  = rows[row_i].get("fs_ip") or ""
                ifn = rows[row_i].get("fs_ifname") or ""
                oper = rows[row_i].get("fs_status") or "unknown"
                rx = rows[row_i].get("dom_rx_dbm")
                ibs = rows[row_i].get("in_bps")
                obs = rows[row_i].get("out_bps")
                rows[row_i]["summary"] = (
                    f"{dev} [{ip_}] {ifn}: oper={oper} "
                    f"rx={(rx if rx is not None else 'n/a')} dBm "
                    f"in={(0.0 if ibs is None else ibs):.0f} out={(0.0 if obs is None else obs):.0f} bps"
                    + ("" if dt is None else f" dt={int(dt)}ms")
                )

    snapshot = {
        "t": now_ts,
        "elapsed_s": round(time.time() - t0, 3),
        "counts": {"olts": cnt_olts, "links": cnt_links, "rows": len(rows)},
        "rows": rows,
    }

    try:
        persist_probe_rows(snapshot, db_path)
    except Exception:
        LOG.exception("switch_probe: persist failed (db_path=%s)", db_path)

    LOG.info("switch_probe: ok olts=%s links=%s rows=%s dt=%.2fs (sleep_s=%.2fs, min_dt_ms=%s)",
             cnt_olts, cnt_links, len(rows), snapshot["elapsed_s"], sleep_s, min_dt_ms)
    return snapshot


# ─────────────────────────────
# Celery Helpers
# ─────────────────────────────

def _patch_orchestrator_poll_by_olt(orch: Orchestrator) -> None:
    """
    Monkey-patch Orchestrator.poll_offline_rows to an OLT-driven version.

    Emits rows with:
      - device_id: ONU id
      - site_id:   region site id from config.LOCATIONS[region]['site']
      - olt_id:    current OLT id
      - status:    'disconnected' | 'offline' (normalized)
      - suspended: bool (direct or endpoint check)
      - last_seen: ISO (best-effort)
      - age_s:     seconds since last_seen (best-effort)
    """
    def _deep_get(d, *path):
        cur = d
        for k in path:
            if not isinstance(cur, dict):
                return None
            cur = cur.get(k)
        return cur

    def _iso_age(iso: str | None):
        if not iso:
            return "", None
        try:
            from datetime import datetime, timezone
            dt = datetime.fromisoformat(str(iso).replace("Z", "+00:00"))
            return dt.strftime("%Y-%m-%dT%H:%M:%S"), int((datetime.now(timezone.utc) - dt).total_seconds())
        except Exception:
            return "", None

    # Budget for extra lookups (e.g., suspended check when missing)
    try:
        from uisp_service import LookupBudget
        def _budget():
            return LookupBudget(max_ops=getattr(SETTINGS, "CRM_LOOKUPS_PER_POLL", 10))
    except Exception:  # super-safe fallback
        class _B:
            def __init__(self, max_ops=0): self.max_ops=max_ops; self.used=0
            def allow(self, n: int = 1):
                if self.used + n > max(0, int(self.max_ops)): return False
                self.used += n; return True
        def _budget(): return _B(getattr(SETTINGS, "CRM_LOOKUPS_PER_POLL", 10))

    def _patched(self, *, regions: list[str]) -> list[dict]:
        rows: list[dict] = []
        locs: dict = getattr(SETTINGS, "LOCATIONS", {}) or {}
        dbg: list[str] = []
        gate = _budget()

        for region in regions:
            cfg = locs.get(region, {}) if isinstance(locs, dict) else {}
            region_site_id = str((cfg.get("site") or "")).strip()
            olt_ids = list(cfg.get("olts") or [])
            if not region_site_id or not olt_ids:
                if self.debug:
                    print(f"[OLT] region={region} missing site or olts; skipping")
                continue

            total = 0
            for olt_id in olt_ids:
                onus = self.svc.nsm.list_onus_by_olt(olt_id) or []
                total += len(onus)
                for onu in onus:
                    if not isinstance(onu, dict):
                        continue
                    did = str(onu.get("id") or onu.get("_id") or "").strip()
                    if not did:
                        continue

                    status = str(
                        _deep_get(onu, "status")
                        or _deep_get(onu, "state")
                        or _deep_get(onu, "identification", "status")
                        or _deep_get(onu, "operating", "state")
                        or ""
                    ).lower().strip()
                    if status not in ("disconnected", "offline"):
                        continue

                    # suspended flag (direct if present; else check site endpoints)
                    v = _deep_get(onu, "suspended")
                    suspended = bool(v) if isinstance(v, bool) else None
                    if suspended is None and gate.allow():
                        try:
                            suspended = bool(self.svc.nsm.endpoint_suspended(site_id=region_site_id, endpoint_id=did))
                        except Exception:
                            suspended = False
                    if not self.include_suspended and suspended:
                        continue

                    last_seen_iso = (
                        _deep_get(onu, "lastSeen")
                        or _deep_get(onu, "last_seen")
                        or _deep_get(onu, "statistics", "lastSeen")
                        or _deep_get(onu, "times", "lastSeen")
                        or None
                    )
                    last_seen_trim, age_s = _iso_age(last_seen_iso)

                    rows.append({
                        "region": region,
                        "device_id": did,
                        "olt_id": str(olt_id),
                        "site_id": region_site_id,
                        "status": "disconnected",
                        "suspended": bool(suspended),
                        "reason": ("suspended" if suspended else "offline"),
                        "last_seen": last_seen_trim,
                        "age_s": age_s,
                    })
            dbg.append(f"{region}:{len(olt_ids)} olts -> {total} onus")

        if self.debug:
            print("OFFLINE ONUs (disconnected/offline; suspended " + ("included" if self.include_suspended else "excluded") + " )")
            print("Regions: " + ",".join(regions) + "  |  OLT sweep: " + "; ".join(dbg))
            print(f"Found: {len(rows)}")
        return rows

    # Bind as instance method
    orch.poll_offline_rows = types.MethodType(_patched, orch)


# ─────────────────────────────
# Celery tasks
# ─────────────────────────────

@app.task(name="tasks.switch_probe")
def switch_probe() -> str:
    """
    FS3900 DOM/traffic probe across configured OLTs (NO notifications).
    Inputs : none
    Output : str one-line status for logs
    SideFX : persists rows to SQLite (storage.persist_probe_rows)
    """
    # switch_probe task:
    with _single_run_lock(LOCK_FS, LOCK_TTL_FS_S) as got:
        if not got:
            msg = f"switch_probe: lock busy; skipping ({LOCK_FS})"
            LOG.info(msg)
            return msg
        snap = _run_switch_probe_once()
        msg = f"switch_probe: ok olts={snap['counts']['olts']} rows={snap['counts']['rows']} dt={snap['elapsed_s']:.2f}s"
        LOG.info(msg)
        return msg

# tasks.py

@app.task(name="tasks.onu_probe")
def onu_probe() -> str:
    """
    Run the canonical ONU outage orchestrator (probe_and_notify.py) once.
    Uses OLT-driven detector for proper olt_id + cluster context.
    Side effects:
      - Updates outage state JSON at PN_STATE_PATH / STATE_PATH
      - Appends completed outages to PN_JOURNAL_PATH / JOURNAL_PATH
      - Sends notifications unless DRY_RUN_NOTIFICATIONS is True
    """
    regions = _resolve_regions()
    debug = bool(int(os.getenv("DEBUG", "0") or "0"))
    dry = bool(getattr(SETTINGS, "DRY_RUN_NOTIFICATIONS", False))
    include_suspended = False

    with _single_run_lock(LOCK_ONU, LOCK_TTL_ONU_S) as got:
        if not got:
            msg = f"onu_probe: lock busy; skipping ({LOCK_ONU})(regions={regions})"
            LOG.info(msg)
            return msg

        try:
            # Build service + state exactly like probe_and_notify main()
            svc = UISPService.from_config(debug=debug)
            state = OutageState(STATE_PATH)

            # Only use test overrides when explicitly in TEST_MODE
            tc = ("13" if TEST_MODE else None)
            te = (TEST_EMAIL_TO if TEST_MODE else None)
            ts = ("7576958080" if TEST_MODE else None)

            orch = Orchestrator(
                svc=svc,
                state=state,
                journal_path=JOURNAL_PATH,
                include_suspended=include_suspended,
                dry_run=dry,
                debug=debug,
                test_client=tc,
                test_email=te,
                test_sms=ts,
            )

            # IMPORTANT: OLT-driven detector so rows carry olt_id & region site_id,
            # enabling cluster classification and FS3900 context.
            _patch_orchestrator_poll_by_olt(orch)

            t0 = time.time()
            orch.orchestrate_once(regions=regions)
            dt = time.time() - t0
            msg = (f"onu_probe: ok regions={regions} dt={dt:.2f}s "
                   f"(dry_run={'yes' if dry else 'no'}, include_suspended={'yes' if include_suspended else 'no'})")
            LOG.info(msg)
            return msg

        except Exception:
            LOG.exception("onu_probe: orchestrate_once failed")
            return "onu_probe: error"



# ── Back-compat task names (safe aliases) ─────────────────────────────────────
@app.task(name="tasks.poll_and_notify")
def poll_and_notify() -> str:
    """Alias to switch_probe (historical name kept for beat)."""
    return switch_probe()

@app.task(name="tasks.probe_and_notify")
def probe_and_notify() -> str:
    """Alias to onu_probe (historical name kept for beat)."""
    return onu_probe()

# ─────────────────────────────
# Daily digest & maintenance
# ─────────────────────────────
@app.task(name="tasks.daily_status_digest")
def daily_status_digest() -> str:
    """
    Compose and send the daily 24h status digest email.
    Input : none
    Output: str status line
    SideFX: email via notify.send_email; optional JSON artifact if configured
    """
    try:
        now = int(time.time())
        since_ts = now - 24 * 3600
        init_probe_db(DB_PATH)
        summary = daily_summary(DB_PATH, since_ts)

        # Optional: write artifact to a file for dashboards
        out_path = getattr(SETTINGS, "DAILY_STATUS_PATH", "") or ""
        if out_path:
            try:
                os.makedirs(os.path.dirname(out_path) or ".", exist_ok=True)
                with open(out_path, "w", encoding="utf-8") as f:
                    json.dump(summary, f, indent=2, sort_keys=True)
            except Exception:
                LOG.exception("daily_status_digest: failed to write DAILY_STATUS_PATH")

        subject, to_hint = _send_daily_status_email(summary)
        msg = f"daily_status_digest: sent -> {to_hint} subj={subject!r}"
        LOG.info(msg)
        return msg
    except Exception:
        LOG.exception("daily_status_digest: failed")
        return "daily_status_digest: error"

@app.task(name="tasks.rollup_5m")
def rollup_5m() -> str:
    """
    Aggregate raw samples into 5m buckets (recent window).
    Input : none
    Output: str status line
    """
    try:
        init_probe_db(DB_PATH)
        db_rollup_5m(DB_PATH, lookback_minutes=60)
        msg = "rollup_5m: ok"
        LOG.info(msg)
        return msg
    except Exception:
        LOG.exception("rollup_5m: failed")
        return "rollup_5m: error"

@app.task(name="tasks.rollup_30m")
def rollup_30m() -> str:
    """
    Aggregate 5m buckets into 30m buckets (recent window).
    Input : none
    Output: str status line
    """
    try:
        init_probe_db(DB_PATH)
        db_rollup_30m(DB_PATH, lookback_hours=24)
        msg = "rollup_30m: ok"
        LOG.info(msg)
        return msg
    except Exception:
        LOG.exception("rollup_30m: failed")
        return "rollup_30m: error"

@app.task(name="tasks.retention_prune")
def retention_prune() -> str:
    """
    Prune old rows per policy.
    Input : none
    Output: str status line
    Policy:
      - raw:    keep 1 day
      - 5m agg: keep 7 days
      - 30m:    keep 365 days
    """
    try:
        init_probe_db(DB_PATH)
        db_retention_prune(DB_PATH, keep_raw_days=1, keep_5m_days=7, keep_30m_days=365)
        msg = "retention_prune: ok (raw=1d, 5m=7d, 30m=365d)"
        LOG.info(msg)
        return msg
    except Exception:
        LOG.exception("retention_prune: failed")
        return "retention_prune: error"

@app.task(name="tasks.nightly_maintenance")
def nightly_maintenance() -> str:
    """
    Nightly rollups + prune (digest is separate on purpose).
    Input : none
    Output: str status line
    """
    try:
        t0 = time.time()
        init_probe_db(DB_PATH)
        db_rollup_5m(DB_PATH, lookback_minutes=60)
        db_rollup_30m(DB_PATH, lookback_hours=24)
        db_retention_prune(DB_PATH, keep_raw_days=1, keep_5m_days=7, keep_30m_days=365)
        dt = time.time() - t0
        msg = f"nightly_maintenance: ok dt={dt:.2f}s"
        LOG.info(msg)
        return msg
    except Exception:
        LOG.exception("nightly_maintenance: failed")
        return "nightly_maintenance: error"

# Legacy alias for old beat entries
@app.task(name="tasks.probe_devices_flat_and_persist")
def probe_devices_flat_and_persist(*args, **kwargs) -> str:
    # Old behavior = FS3900 probe + persist; our switch_probe does exactly that.
    return switch_probe()

@app.task(name="tasks.send_daily_status_email")
def send_daily_status_email_legacy(window_hours: int = 24) -> str:
    return daily_status_digest()

