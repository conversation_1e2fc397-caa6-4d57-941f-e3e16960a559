from __future__ import annotations
import os, sqlite3, time, re
from contextlib import contextmanager
from typing import Dict, Any, Iterable, Optional, Tuple, List

# ---------------------------
# SQLite connection + schema
# ---------------------------

@contextmanager
def _sqlite_conn(db_path: str):
    """
    Context-managed SQLite connection configured for WAL and sane durability.
    Single writer is fine for our cadence. Readers won't block writers in WAL mode.
    """
    # db_path in your tasks is abspath'd, so dirname is non-empty.
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    conn = sqlite3.connect(db_path, timeout=10, isolation_level=None)  # autocommit mode
    try:
        conn.execute("PRAGMA journal_mode=WAL;")
        conn.execute("PRAGMA synchronous=NORMAL;")
        conn.execute("PRAGMA foreign_keys=ON;")
        yield conn
    finally:
        conn.close()

def init_probe_db(db_path: str):
    with _sqlite_conn(db_path) as conn:
        cur = conn.cursor()

        # --- RAW samples (lean) ---
        cur.execute("""
        CREATE TABLE IF NOT EXISTS samples_raw (
            ts              INTEGER NOT NULL,
            olt_id          TEXT    NOT NULL,
            olt_up          INTEGER,
            fs_up           INTEGER,
            fs_status       TEXT,
            in_bps          REAL,
            out_bps         REAL,
            has_traffic     INTEGER,
            dt_ms           INTEGER,
            dom_rx_dbm      REAL,
            dom_tx_dbm      REAL,
            dom_switch      TEXT,
            error           TEXT,
            summary         TEXT
        );
        """)
        cur.execute("CREATE INDEX IF NOT EXISTS ix_raw_ts ON samples_raw(ts);")
        cur.execute("CREATE INDEX IF NOT EXISTS ix_raw_olt_ts ON samples_raw(olt_id, ts);")

        # Add columns that may be missing in older DB files (idempotent).
        for sql in [
            "ALTER TABLE samples_raw ADD COLUMN dom_tx_dbm REAL",
            "ALTER TABLE samples_raw ADD COLUMN dom_switch TEXT",
            "ALTER TABLE samples_raw ADD COLUMN dom_temp_c REAL",
            "ALTER TABLE samples_raw ADD COLUMN dom_vcc_v REAL",
            "ALTER TABLE samples_raw ADD COLUMN dom_bias_ma REAL",
        ]:
            try:
                cur.execute(sql)
            except Exception:
                pass

        # --- Static device metadata (helper) ---
        cur.execute("""
        CREATE TABLE IF NOT EXISTS devices (
            olt_id      TEXT PRIMARY KEY,
            device_name TEXT,
            olt_ip      TEXT,
            fs_ip       TEXT,
            fs_ifname   TEXT
        );
        """)

        # --- 5-minute aggregates ---
        cur.execute("""
        CREATE TABLE IF NOT EXISTS agg_5m (
            bucket_ts          INTEGER NOT NULL,
            olt_id             TEXT    NOT NULL,
            device_name        TEXT,
            fs_ifname          TEXT,
            fs_status_last     TEXT,
            avg_in_bps         REAL,
            avg_out_bps        REAL,
            pct_has_traffic    REAL,
            avg_dom_rx_dbm     REAL,
            avg_dom_tx_dbm     REAL,
            worst_dom_severity INTEGER,
            samples            INTEGER NOT NULL,
            avg_dom_temp_c     REAL,
            avg_dom_vcc_v      REAL,
            avg_dom_bias_ma    REAL,
            PRIMARY KEY (bucket_ts, olt_id)
        );
        """)
        cur.execute("CREATE INDEX IF NOT EXISTS ix_agg5m_olt ON agg_5m(olt_id, bucket_ts);")

        # --- 30-minute aggregates ---
        cur.execute("""
        CREATE TABLE IF NOT EXISTS agg_30m (
            bucket_ts          INTEGER NOT NULL,
            olt_id             TEXT    NOT NULL,
            device_name        TEXT,
            fs_ifname          TEXT,
            fs_status_last     TEXT,
            avg_in_bps         REAL,
            avg_out_bps        REAL,
            pct_has_traffic    REAL,
            avg_dom_rx_dbm     REAL,
            avg_dom_tx_dbm     REAL,
            worst_dom_severity INTEGER,
            samples            INTEGER NOT NULL,
            avg_dom_temp_c     REAL,
            avg_dom_vcc_v      REAL,
            avg_dom_bias_ma    REAL,
            PRIMARY KEY (bucket_ts, olt_id)
        );
        """)
        cur.execute("CREATE INDEX IF NOT EXISTS ix_agg30m_olt ON agg_30m(olt_id, bucket_ts);")

        # --- NEW: DOM threshold cache (per fs_ip,port) ---
        cur.execute("""
        CREATE TABLE IF NOT EXISTS dom_thresholds(
            fs_ip        TEXT    NOT NULL,
            port         INTEGER NOT NULL,
            rx_low_alarm REAL,
            rx_low_warn  REAL,
            rx_high_warn REAL,
            rx_high_alarm REAL,
            updated_ts   INTEGER NOT NULL,
            PRIMARY KEY (fs_ip, port)
        );
        """)
        cur.execute("CREATE INDEX IF NOT EXISTS ix_thr_fs_ts ON dom_thresholds(fs_ip, updated_ts);")

        ensure_dom_columns(conn)
        conn.commit()

#------ Schema Helpers -----
def ensure_dom_columns(conn):
    """Idempotent: adds dom_tx_dbm + dom_switch if they don't exist."""
    cur = conn.cursor()
    for sql in [
        "ALTER TABLE samples_raw ADD COLUMN dom_tx_dbm REAL",
        "ALTER TABLE samples_raw ADD COLUMN dom_switch TEXT"
    ]:
        try:
            cur.execute(sql)
        except Exception:
            pass
    conn.commit()

# ---------------------------
# Persistence (unchanged)
# ---------------------------
def persist_probe_rows(snapshot: Dict[str, Any], db_path: str) -> None:
    """
    Write flattened rows; safe with NULLs. Also maintains 'devices'.
    """
    rows: Iterable[Dict[str, Any]] = (snapshot or {}).get("rows") or []
    ts = int((snapshot or {}).get("t") or time.time())

    with _sqlite_conn(db_path) as conn:
        ensure_dom_columns(conn)
        cur = conn.cursor()
        cur.execute("BEGIN IMMEDIATE;")
        try:
            for r in rows:
                # upsert device meta
                cur.execute("""
                    INSERT INTO devices (olt_id, device_name, olt_ip, fs_ip, fs_ifname)
                    VALUES (?,?,?,?,?)
                    ON CONFLICT(olt_id) DO UPDATE SET
                        device_name=excluded.device_name,
                        olt_ip=excluded.olt_ip,
                        fs_ip=excluded.fs_ip,
                        fs_ifname=excluded.fs_ifname
                """, (
                    r.get("olt_id"),
                    r.get("device_name") or r.get("name"),
                    r.get("olt_ip"),
                    r.get("fs_ip"),
                    r.get("fs_ifname"),
                ))

                # raw sample
                cur.execute("""
                    INSERT INTO samples_raw (
                        ts, olt_id, olt_up, fs_up, fs_status,
                        in_bps, out_bps, has_traffic, dt_ms,
                        dom_rx_dbm, dom_tx_dbm, dom_switch,
                        dom_temp_c, dom_vcc_v, dom_bias_ma,
                        error, summary
                    ) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
                """, (
                    ts,
                    r.get("olt_id"),
                    _b(r.get("olt_up")),
                    _b(r.get("fs_up")),
                    r.get("fs_status"),
                    r.get("in_bps"),
                    r.get("out_bps"),
                    _b(r.get("has_traffic")),
                    r.get("dt_ms"),
                    r.get("dom_rx_dbm"),
                    r.get("dom_tx_dbm"),
                    r.get("dom_switch"),
                    r.get("dom_temp_c"),
                    r.get("dom_vcc_v"),
                    r.get("dom_bias_ma"),
                    r.get("error"),
                    r.get("summary"),
                ))
            cur.execute("COMMIT;")
        except Exception:
            cur.execute("ROLLBACK;")
            raise

def _b(x: Optional[bool]) -> Optional[int]:
    if x is True: return 1
    if x is False: return 0
    return None

def _bucket(ts: int, seconds: int) -> int:
    return (ts // seconds) * seconds

# ---------------------------
# Rollups & Retention (unchanged)
# ---------------------------
def rollup_5m(db_path: str, lookback_minutes: int = 15):
    now = int(time.time())
    win_start = now - lookback_minutes * 60
    with _sqlite_conn(db_path) as conn:
        cur = conn.cursor()
        cur.execute("""
        WITH windowed AS (
            SELECT
              ((ts/300)*300) AS bucket_ts,
              r.olt_id,
              r.fs_status,
              r.in_bps, r.out_bps,
              CASE r.has_traffic WHEN 1 THEN 1.0 ELSE 0.0 END AS has_trf,
              r.dom_rx_dbm, r.dom_tx_dbm,
              CASE r.dom_switch
                   WHEN 'alarm'   THEN 3
                   WHEN 'caution' THEN 2
                   WHEN 'normal'  THEN 1
                   ELSE 0
              END AS sev,
              r.ts
            FROM samples_raw r
            WHERE r.ts >= ?
        ),
        last_status AS (
            SELECT w.bucket_ts, w.olt_id,
                   (SELECT fs_status FROM windowed w2
                     WHERE w2.bucket_ts=w.bucket_ts AND w2.olt_id=w.olt_id
                     ORDER BY w2.ts DESC LIMIT 1) AS fs_status_last
            FROM windowed w
            GROUP BY w.bucket_ts, w.olt_id
        ),
        aggs AS (
            SELECT
              w.bucket_ts, w.olt_id,
              AVG(w.in_bps)  AS avg_in_bps,
              AVG(w.out_bps) AS avg_out_bps,
              AVG(w.has_trf) AS pct_has_traffic,
              AVG(w.dom_rx_dbm) AS avg_dom_rx_dbm,
              AVG(w.dom_tx_dbm) AS avg_dom_tx_dbm,
              MAX(w.sev) AS worst_dom_severity,
              COUNT(*) AS samples
            FROM windowed w
            GROUP BY w.bucket_ts, w.olt_id
        )
        INSERT INTO agg_5m (
            bucket_ts, olt_id, device_name, fs_ifname,
            fs_status_last, avg_in_bps, avg_out_bps, pct_has_traffic,
            avg_dom_rx_dbm, avg_dom_tx_dbm, worst_dom_severity, samples
        )
        SELECT
            a.bucket_ts, a.olt_id,
            d.device_name, d.fs_ifname,
            s.fs_status_last, a.avg_in_bps, a.avg_out_bps, a.pct_has_traffic,
            a.avg_dom_rx_dbm, a.avg_dom_tx_dbm, a.worst_dom_severity, a.samples
        FROM aggs a
        JOIN last_status s ON s.bucket_ts=a.bucket_ts AND s.olt_id=a.olt_id
        LEFT JOIN devices d ON d.olt_id=a.olt_id
        ON CONFLICT(bucket_ts, olt_id) DO UPDATE SET
            device_name=excluded.device_name,
            fs_ifname=excluded.fs_ifname,
            fs_status_last=excluded.fs_status_last,
            avg_in_bps=excluded.avg_in_bps,
            avg_out_bps=excluded.avg_out_bps,
            pct_has_traffic=excluded.pct_has_traffic,
            avg_dom_rx_dbm=excluded.avg_dom_rx_dbm,
            avg_dom_tx_dbm=excluded.avg_dom_tx_dbm,
            worst_dom_severity=excluded.worst_dom_severity,
            samples=excluded.samples;
        """, (win_start,))
        conn.commit()

def rollup_30m(db_path: str, lookback_hours: int = 6):
    now = int(time.time())
    win_start = now - lookback_hours * 3600
    with _sqlite_conn(db_path) as conn:
        cur = conn.cursor()
        cur.execute("""
        WITH windowed AS (
            SELECT ((bucket_ts/1800)*1800) AS bucket_ts_30m, *
            FROM agg_5m
            WHERE bucket_ts >= ?
        ),
        aggs AS (
            SELECT
              bucket_ts_30m AS bucket_ts,
              olt_id,
              MAX(device_name) AS device_name,
              MAX(fs_ifname) AS fs_ifname,
              AVG(avg_in_bps) AS avg_in_bps,
              AVG(avg_out_bps) AS avg_out_bps,
              AVG(pct_has_traffic) AS pct_has_traffic,
              AVG(avg_dom_rx_dbm) AS avg_dom_rx_dbm,
              AVG(avg_dom_tx_dbm) AS avg_dom_tx_dbm,
              MAX(worst_dom_severity) AS worst_dom_severity,
              SUM(samples) AS samples
            FROM windowed
            GROUP BY bucket_ts_30m, olt_id
        ),
        last_status AS (
            SELECT
              ((bucket_ts/1800)*1800) AS bucket_ts, olt_id,
              (SELECT fs_status_last FROM agg_5m a2
                WHERE ((a2.bucket_ts/1800)*1800)=((a1.bucket_ts/1800)*1800)
                  AND a2.olt_id=a1.olt_id
                ORDER BY a2.bucket_ts DESC LIMIT 1) AS fs_status_last
            FROM agg_5m a1
            WHERE a1.bucket_ts >= ?
            GROUP BY ((bucket_ts/1800)*1800), olt_id
        )
        INSERT INTO agg_30m (
            bucket_ts, olt_id, device_name, fs_ifname,
            fs_status_last, avg_in_bps, avg_out_bps, pct_has_traffic,
            avg_dom_rx_dbm, avg_dom_tx_dbm, worst_dom_severity, samples
        )
        SELECT
            a.bucket_ts, a.olt_id, a.device_name, a.fs_ifname,
            s.fs_status_last, a.avg_in_bps, a.avg_out_bps, a.pct_has_traffic,
            a.avg_dom_rx_dbm, a.avg_dom_tx_dbm, a.worst_dom_severity, a.samples
        FROM aggs a
        JOIN last_status s USING(bucket_ts, olt_id)
        ON CONFLICT(bucket_ts, olt_id) DO UPDATE SET
            device_name=excluded.device_name,
            fs_ifname=excluded.fs_ifname,
            fs_status_last=excluded.fs_status_last,
            avg_in_bps=excluded.avg_in_bps,
            avg_out_bps=excluded.avg_out_bps,
            pct_has_traffic=excluded.pct_has_traffic,
            avg_dom_rx_dbm=excluded.avg_dom_rx_dbm,
            avg_dom_tx_dbm=excluded.avg_dom_tx_dbm,
            worst_dom_severity=excluded.worst_dom_severity,
            samples=excluded.samples;
        """, (win_start, win_start))
        conn.commit()

def retention_prune(db_path: str,
                    keep_raw_days: int = 1,
                    keep_5m_days: int = 7,
                    keep_30m_days: int = 365):
    now = int(time.time())
    raw_cut   = now - keep_raw_days * 86400
    ag5_cut   = now - keep_5m_days * 86400
    ag30_cut  = now - keep_30m_days * 86400
    with _sqlite_conn(db_path) as conn:
        cur = conn.cursor()
        cur.execute("DELETE FROM samples_raw WHERE ts < ?", (raw_cut,))
        cur.execute("DELETE FROM agg_5m WHERE bucket_ts < ?", (ag5_cut,))
        cur.execute("DELETE FROM agg_30m WHERE bucket_ts < ?", (ag30_cut,))
        conn.commit()

# ---------------------------
# NEW: Threshold cache helpers
# ---------------------------
def get_cached_thresholds(db_path: str, fs_ip: str, port: int, max_age_s: int = 86400) -> Optional[dict]:
    with _sqlite_conn(db_path) as conn:
        row = conn.execute("""
            SELECT rx_low_alarm, rx_low_warn, rx_high_warn, rx_high_alarm, updated_ts
            FROM dom_thresholds WHERE fs_ip=? AND port=?;
        """, (fs_ip, port)).fetchone()
    if not row:
        return None
    la, lw, hw, ha, ts = row
    if int(time.time()) - int(ts) > max_age_s:
        return None
    return {"rx_dbm": {
        "low_alarm": la, "low_warn": lw, "high_warn": hw, "high_alarm": ha
    }}

def upsert_thresholds(db_path: str, fs_ip: str, port: int, thr: dict) -> None:
    rx = (thr or {}).get("rx_dbm") or {}
    with _sqlite_conn(db_path) as conn:
        conn.execute("""
            INSERT INTO dom_thresholds(fs_ip, port, rx_low_alarm, rx_low_warn, rx_high_warn, rx_high_alarm, updated_ts)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            ON CONFLICT(fs_ip, port) DO UPDATE SET
                rx_low_alarm=excluded.rx_low_alarm,
                rx_low_warn =excluded.rx_low_warn,
                rx_high_warn=excluded.rx_high_warn,
                rx_high_alarm=excluded.rx_high_alarm,
                updated_ts =excluded.updated_ts;
        """, (fs_ip, port, rx.get("low_alarm"), rx.get("low_warn"),
              rx.get("high_warn"), rx.get("high_alarm"), int(time.time())))

# ---------------------------
# NEW: Daily summary + email
# ---------------------------
_PORT_RE = re.compile(r"\b1\s*/\s*(\d+)\b", re.I)
def _port_from_ifname(ifname: str | None) -> Optional[int]:
    if not ifname:
        return None
    m = _PORT_RE.search(ifname)
    return int(m.group(1)) if m else None

def daily_summary(db_path: str, since_ts: int) -> dict[str, Any]:
    """
    Returns {'outages': [...], 'dom_alerts': [...]}
    - outages: any samples with fs_status != 'up' (grouped by olt/fs)
    - dom_alerts: rx_dbm vs cached thresholds (fs_ip+port)
    """
    with _sqlite_conn(db_path) as conn:
        # Outages (join devices to get names/ifnames)
        outages = conn.execute("""
            SELECT r.olt_id, COALESCE(d.device_name, r.olt_id) AS name,
                   d.fs_ip, d.fs_ifname,
                   MIN(r.ts) AS first_ts, MAX(r.ts) AS last_ts,
                   SUM(CASE WHEN r.fs_status='up' THEN 0 ELSE 1 END) AS bad_samples
            FROM samples_raw r
            LEFT JOIN devices d USING(olt_id)
            WHERE r.ts >= ? AND (r.fs_status IS NULL OR r.fs_status!='up')
            GROUP BY r.olt_id, name, d.fs_ip, d.fs_ifname
            ORDER BY bad_samples DESC, last_ts DESC;
        """, (since_ts,)).fetchall()

        # DOM rows with rx power
        dom_rows = conn.execute("""
            SELECT r.ts, d.fs_ip, d.fs_ifname, r.dom_rx_dbm
            FROM samples_raw r
            JOIN devices d USING(olt_id)
            WHERE r.ts >= ? AND r.dom_rx_dbm IS NOT NULL;
        """, (since_ts,)).fetchall()

    alerts: List[dict] = []
    thr_cache: dict[tuple[str,int], dict] = {}
    now = int(time.time())

    for ts, fs_ip, ifname, rx_dbm in dom_rows:
        port = _port_from_ifname(ifname)
        if port is None:
            continue
        key = (fs_ip, port)
        thr = thr_cache.get(key)
        if thr is None:
            thr = get_cached_thresholds(db_path, fs_ip, port, max_age_s=365*24*3600)
            thr_cache[key] = thr
        if not thr:
            continue

        rx = thr.get("rx_dbm") or {}
        la, lw, hw, ha = rx.get("low_alarm"), rx.get("low_warn"), rx.get("high_warn"), rx.get("high_alarm")
        state = None
        if la is not None and rx_dbm <= la: state = "low_alarm"
        elif lw is not None and rx_dbm <= lw: state = "low_warn"
        elif ha is not None and rx_dbm >= ha: state = "high_alarm"
        elif hw is not None and rx_dbm >= hw: state = "high_warn"

        if state:
            alerts.append({
                "ts": ts, "fs_ip": fs_ip, "port": port,
                "rx_dbm": rx_dbm, "state": state,
                "limits": {"low_alarm": la, "low_warn": lw, "high_warn": hw, "high_alarm": ha}
            })

    return {
        "outages": [dict(zip(
            ["olt_id","name","fs_ip","fs_ifname","first_ts","last_ts","bad_samples"], row
        )) for row in outages],
        "dom_alerts": alerts,
    }

def compose_daily_email(summary: dict[str, Any]) -> tuple[str,str]:
    now = time.strftime("%Y-%m-%d")
    subj = f"[NOC] Daily report {now}: outages & optical alerts"
    lines = []
    outs = summary.get("outages", [])
    lines.append(f"Outages / degraded (last 24h): {len(outs)}")
    for o in outs[:100]:
        first = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(o["first_ts"]))
        last  = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(o["last_ts"]))
        lines.append(f"- {o['name']} {o['fs_ifname']}  bad={o['bad_samples']}  window={first} → {last}")

    doms = summary.get("dom_alerts", [])
    lines.append("")
    lines.append(f"Optical power warnings/alarms (last 24h): {len(doms)}")
    for a in doms[:200]:
        ts = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(a["ts"]))
        lines.append(
            f"- {a['fs_ip']} port {a['port']}  rx={a['rx_dbm']:.2f} dBm  state={a['state']}  limits={a['limits']}"
        )
    body = "\n".join(lines) if lines else "No issues in the last 24 hours."
    return subj, body
