from __future__ import annotations

"""
uisp_service.py — Thin, well-documented NSM/CRM integration layer
=================================================================

Purpose
-------
A single importable module that encapsulates *only* the UISP Network (NSM)
fetches and the UISP CRM ticket/customer operations. Escalation heuristics,
state machines, and notification fan-out **stay in probe_and_notify.py** (or
elsewhere). This module is intentionally boring: predictable I/O shapes, no
side effects beyond HTTP, and clear extension seams.

Design goals
------------
- **Deterministic helpers** for NSM polling (e.g., list ONUs by OLT) and site
  endpoint inspection (for strict suspended vs offline classification).
- **CRM primitives** (create/update/close ticket; read client/ticket) with a
  robust PATCH implementation that works across minor API shape variations.
- **No env-var drift**: reads *only* from `config.settings`.
- **Testable**: every outward action lives behind a method; dry-run printing is
  available to observe payloads during tests.
- **Annotated & noted**: all methods carry type hints and inline DEVNOTEs.

Typical usage (inside probe_and_notify.py)
-----------------------------------------
>>> from uisp_service import UISPService
>>> svc = UISPService.from_config(debug=False)
>>> onus = svc.nsm.list_onus_by_olt(olt_id)
>>> # strict suspended detection via region site endpoints when you need it
>>> is_susp = svc.nsm.endpoint_suspended(site_id=region_site_id, endpoint_id=endpoint_site_id)
>>> # CRM: map to client, create/update/close tickets
>>> client = svc.crm.get_client("13")
>>> tid = svc.crm.create_ticket(client_id=13, subject="Outage", note="ONU offline", dry=False)
>>> svc.crm.close_ticket(tid, note="Recovered", dry=False)
>>> hc = svc.health_check()
>>> uisp_ping_ok = (hc["loss_pct"] < 100)

Module structure
----------------
- NSMClient: UISP Network calls
- CRMClient: UISP CRM calls (tickets/clients)
- UISPService: convenience wrapper composed of NSMClient + CRMClient
- LookupBudget: tiny helper for throttling (e.g., CRM lookups per poll)

Helper inventory (keep this list in sync)
-----------------------------------------
- _safe_json()                 → tolerant JSON decode for requests
- _iso_now()                   → UTC ISO string for payloads/logging
- LookupBudget.allow()         → decrement/check budget gate
- NSMClient.list_onus_by_olt() → fetch ONUs under OLT (with fallbacks)
- NSMClient.get_site()         → fetch site by id (with fallback list)
- NSMClient.get_onu_detail()   → fetch full onu by id (optional)
- NSMClient.endpoint_suspended() → strict boolean from site endpoints
- CRMClient.get_client()       → retrieve CRM client record
- CRMClient.create_ticket()    → POST /ticketing/tickets
- CRMClient.update_ticket()    → PATCH ticket (status + comment)
- CRMClient.close_ticket()     → thin wrapper around update_ticket
- CRMClient.get_ticket()       → GET ticket for verification

DEVNOTEs you can feed back verbatim
-----------------------------------
- DEVNOTE(transport): If a future UISP update changes endpoints, paste the 4xx
  body here; we’ll add another fallback path or parameter tweak.
- DEVNOTE(status-map): If your CRM uses `ticketStatusId` instead of `status`,
  say so; we’ll add a switch mapping without breaking the public method.
- DEVNOTE/sites-endpoints): If `/sites/{id}` stops exposing `description.endpoints`,
  turn on `prefer_sites_devices=True` in NSMClient to try `/sites/{id}/devices`.

"""

from dataclasses import dataclass
from typing import Any, Dict, Iterable, List, Optional, Tuple
import json
import os
import time
import socket, subprocess

# Canonical settings (NO DRIFT)
from config import settings  # type: ignore

try:
    import requests  # type: ignore
except Exception:  # pragma: no cover - optional in some test envs
    requests = None  # type: ignore


# -----------------------------
# Small utilities & throttling
# -----------------------------

def _safe_json(text: str) -> Any:
    try:
        return json.loads(text)
    except Exception:
        return None


def _iso_now() -> str:
    # ISO-8601 UTC without microseconds for readability
    return time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime())


@dataclass
class LookupBudget:
    """Simple decrementing budget gate.

    Example: budget = LookupBudget(max_ops=settings.CRM_LOOKUPS_PER_POLL)
             if budget.allow(): do_lookup()
    """
    max_ops: int
    used: int = 0

    def allow(self, *, n: int = 1) -> bool:
        if self.used + n > max(0, int(self.max_ops)):
            return False
        self.used += n
        return True


# -----------------------------
# UISP Network (NSM) client
# -----------------------------

class NSMClient:
    """Minimal UISP Network client.

    Focus: OLT→ONU listing and site/endpoint inspection, no guesses.
    """

    def __init__(self, base_url: str, token: str, *, timeout_s: int = 10, debug: bool = False,
                 prefer_sites_devices: bool = False) -> None:
        self.base_url = base_url.rstrip("/")
        self.token = token
        self.timeout_s = timeout_s
        self.debug = debug
        self.prefer_sites_devices = prefer_sites_devices
        self._session = requests.Session() if requests else None

    # ---- internals ----
    def _headers(self) -> Dict[str, str]:
        return {"x-auth-token": self.token, "Accept": "application/json"}

    def _get(self, path: str, *, params: Optional[dict] = None) -> Tuple[int, str]:
        if not self._session:
            return 0, ""
        url = f"{self.base_url}{path}"
        try:
            r = self._session.get(url, headers=self._headers(), params=params, timeout=self.timeout_s)
            if self.debug:
                print(f"[NSM] GET {url} params={params} -> {r.status_code} bytes={len(r.text)}")
            return r.status_code, r.text
        except Exception as e:
            if self.debug:
                print(f"[NSM] GET {url} error={e}")
            return -1, ""

    # ---- public API ----
    def list_onus_by_olt(self, olt_id: str) -> List[Dict[str, Any]]:
        """Return ONUs under a given OLT. Try modern and legacy shapes.

        Fallback order: /devices/onus → /devices?type=onu → /devices?role=onu
        """
        # 1) /devices/onus?parentId
        code, text = self._get("/devices/onus", params={"parentId": olt_id})
        if code == 200:
            data = _safe_json(text) or []
            if isinstance(data, list) and data:
                return data
            if isinstance(data, dict):
                items = data.get("items") or data.get("data") or []
                if items:
                    return items  # type: ignore[return-value]
        # 2) /devices?type=onu
        code, text = self._get("/devices", params={"type": "onu", "parentId": olt_id})
        if code == 200:
            data = _safe_json(text) or []
            if isinstance(data, list) and data:
                return data
            if isinstance(data, dict):
                items = data.get("items") or data.get("data") or []
                if items:
                    return items  # type: ignore[return-value]
        # 3) /devices?role=onu
        code, text = self._get("/devices", params={"role": "onu", "parentId": olt_id})
        if code == 200:
            data = _safe_json(text) or []
            if isinstance(data, list) and data:
                return data
            if isinstance(data, dict):
                items = data.get("items") or data.get("data") or []
                if items:
                    return items  # type: ignore[return-value]
        return []

    # uisp_service.py  (inside class NSMClient)
    def list_endpoints_for_site(self, site_id: str) -> list[dict]:
        """Return the endpoint list for a site (children 'endpoint' objects)."""
        site = self.get_site(site_id) or {}
        desc = site.get("description") or {}
        endpoints = desc.get("endpoints") or []
        return endpoints if isinstance(endpoints, list) else []

    def get_site(self, site_id: str) -> Dict[str, Any]:
        """Return a site dict. Tries exact then list+filter as fallback."""
        code, text = self._get(f"/sites/{site_id}")
        if code == 200:
            data = _safe_json(text)
            return data or {}
        # fallback list
        code, text = self._get("/sites", params={"id": site_id})
        if code == 200:
            data = _safe_json(text) or []
            if isinstance(data, list):
                for s in data:
                    if str(s.get("id")) == str(site_id):
                        return s
                return data[0] if data else {}
            if isinstance(data, dict):
                return data
        return {}

    def get_onu_detail(self, onu_id: str) -> Dict[str, Any]:
        """Optional: fetch full ONU record if needed (e.g., for lastSeen)."""
        code, text = self._get(f"/devices/onus/{onu_id}")
        if code == 200:
            return _safe_json(text) or {}
        # sometimes /devices/{id} carries the same document
        code, text = self._get(f"/devices/{onu_id}")
        if code == 200:
            return _safe_json(text) or {}
        return {}

    def endpoint_suspended(self, *, site_id: str, endpoint_id: str) -> bool:
        """Strict suspended flag using region *site* endpoint list.

        This matches Ronald's deterministic rule: if an endpoint entry shows
        `status == disconnected` and `suspended == false` → treat as OFFLINE.
        Else, if `suspended == true` → treat as SUSPENDED. We only return the
        boolean; the caller decides on labeling.
        """
        # Preferred: /sites/{id}
        code, text = self._get(f"/sites/{site_id}")
        if code == 200:
            data = _safe_json(text) or {}
            endpoints = ((data.get("description") or {}).get("endpoints") or [])
            if isinstance(endpoints, list):
                for ep in endpoints:
                    if str(ep.get("id")) == str(endpoint_id):
                        return bool(ep.get("suspended") is True)
        # Optional fallback: /sites/{id}/devices (some builds)
        if self.prefer_sites_devices:
            code, text = self._get(f"/sites/{site_id}/devices")
            if code == 200:
                arr = _safe_json(text) or []
                if isinstance(arr, list):
                    for d in arr:
                        if str(d.get("id")) == str(endpoint_id):
                            v = d.get("suspended")
                            if isinstance(v, bool):
                                return v
        return False


# -----------------------------
# UISP CRM client
# -----------------------------

class CRMClient:
    """UISP CRM client for clients and tickets."""

    def __init__(self, base_url: str, token: str, *, timeout_s: int = 10, debug: bool = False) -> None:
        self.base_url = base_url.rstrip("/")
        self.token = token
        self.timeout_s = timeout_s
        self.debug = debug
        self._session = requests.Session() if requests else None

    # ---- internals ----
    def _headers(self) -> Dict[str, str]:
        return {
            "x-auth-token": self.token,
            "Accept": "application/json",
            "Content-Type": "application/json",
        }

    def _get(self, path: str, *, params: Optional[dict] = None) -> Tuple[int, str]:
        if not self._session:
            return 0, ""
        url = f"{self.base_url}{path}"
        try:
            r = self._session.get(url, headers=self._headers(), params=params, timeout=self.timeout_s)
            if self.debug:
                print(f"[CRM] GET {url} params={params} -> {r.status_code}")
            return r.status_code, r.text
        except Exception as e:
            if self.debug:
                print(f"[CRM] GET {url} error={e}")
            return -1, ""

    def _post(self, path: str, body: dict) -> Tuple[int, str]:
        if not self._session:
            return 0, ""
        url = f"{self.base_url}{path}"
        text = json.dumps(body)
        try:
            if self.debug:
                print(f"[CRM] POST {url} body={text}")
            r = self._session.post(url, headers=self._headers(), data=text, timeout=self.timeout_s)
            if self.debug:
                print(f"[CRM] -> {r.status_code} {r.text[:240]}")
            return r.status_code, r.text
        except Exception as e:
            if self.debug:
                print(f"[CRM] POST {url} error={e}")
            return -1, ""

    def _patch(self, path: str, body: dict) -> Tuple[int, str]:
        if not self._session:
            return 0, ""
        url = f"{self.base_url}{path}"
        text = json.dumps(body)
        try:
            if self.debug:
                print(f"[CRM] PATCH {url} body={text}")
            r = self._session.patch(url, headers=self._headers(), data=text, timeout=self.timeout_s)
            if self.debug:
                print(f"[CRM] -> {r.status_code} {r.text[:240]}")
            return r.status_code, r.text
        except Exception as e:
            if self.debug:
                print(f"[CRM] PATCH {url} error={e}")
            return -1, ""

    # ---- public API ----
    def get_client(self, client_id: str | int) -> Dict[str, Any]:
        code, text = self._get(f"/clients/{client_id}")
        if code == 200:
            return _safe_json(text) or {}
        return {}

    def get_ticket(self, ticket_id: str | int) -> Dict[str, Any]:
        # Try modern then classic path
        for path in (f"/ticketing/tickets/{ticket_id}", f"/tickets/{ticket_id}"):
            code, text = self._get(path)
            if code == 200:
                return _safe_json(text) or {}
        return {}

    def create_ticket(
        self,
        *,
        client_id: Optional[int | str] = None,
        subject: str,
        note: Optional[str] = None,
        assigned_group_id: Optional[int] = None,
        assigned_user_id: Optional[int] = None,
        created_at: Optional[str] = None,
        public: bool = False,
        dry: bool = True,
    ) -> Optional[str]:
        """POST a new ticket. Returns ticket id (string) or None on failure.

        API quirk: If `client_id` is not set, server requires either
        `emailFromAddress` or `phoneFrom` to emulate IMAP/SMS import. We
        normally pass `client_id`.
        """
        payload: Dict[str, Any] = {
            "subject": subject,
            "public": bool(public),
        }
        if client_id is not None:
            payload["clientId"] = int(client_id)
        if note:
            payload["activity"] = [{"type": "comment", "public": bool(public), "text": note}]
        if assigned_group_id:
            payload["assignedGroupId"] = int(assigned_group_id)
        if assigned_user_id:
            payload["assignedUserId"] = int(assigned_user_id)
        payload["createdAt"] = created_at or _iso_now()

        if dry:
            print(f"[TICKET] (dry) create payload={payload}")
            return "DRY-TICKET"

        code, text = self._post("/ticketing/tickets", payload)
        if code in (200, 201):
            data = _safe_json(text) or {}
            tid = data.get("id") or data.get("ticketId") or data.get("number")
            return str(tid) if tid is not None else None
        # Legacy path fallback
        if code in (404, 405):
            code2, text2 = self._post("/tickets", payload)
            if code2 in (200, 201):
                data2 = _safe_json(text2) or {}
                tid = data2.get("id") or data2.get("ticketId") or data2.get("number")
                return str(tid) if tid is not None else None
        return None

    def update_ticket(
        self,
        ticket_id: str | int,
        *,
        note: Optional[str] = None,
        status: Optional[str] = None,
        public: bool = False,
        dry: bool = True,
    ) -> bool:
        """PATCH ticket with optional comment and/or status change."""
        payload: Dict[str, Any] = {}
        if status:
            norm = str(status).strip().lower()
            payload["status"] = {
                "open": "open",
                "opened": "open",
                "pending": "pending",
                "hold": "pending",
                "closed": "closed",
                "close": "closed",
                "solved": "closed",
                "resolved": "closed",
            }.get(norm, status)
        if note:
            payload["activity"] = [{"type": "comment", "public": bool(public), "text": note}]

        if dry:
            print(f"[TICKET] (dry) update id={ticket_id} payload={payload}")
            return True

        for path in (f"/ticketing/tickets/{ticket_id}", f"/tickets/{ticket_id}"):
            code, _ = self._patch(path, payload)
            if code in (200, 202, 204):
                return True
        return False

    def close_ticket(
        self,
        ticket_id: str | int,
        *,
        note: Optional[str] = None,
        public: bool = False,
        dry: bool = True,
    ) -> bool:
        return self.update_ticket(ticket_id, note=note, status="closed", public=public, dry=dry)


# -----------------------------
# Facade / composition layer
# -----------------------------

class UISPService:
    """Thin wrapper that wires NSM + CRM and exposes throttled helpers.

    This is intentionally *not* a god object. It:
    - builds the two clients from `config.settings`
    - offers a per-run `LookupBudget` for CRM lookups to avoid hammering
    - provides convenience helpers that combine both sides where useful
    """

    def __init__(self, nsm: NSMClient, crm: CRMClient, *, crm_budget: Optional[int] = None) -> None:
        self.nsm = nsm
        self.crm = crm
        self.crm_budget = LookupBudget(max_ops=int(crm_budget or getattr(settings, "CRM_LOOKUPS_PER_POLL", 50)))

    @classmethod
    def from_config(cls, *, debug: bool = False) -> "UISPService":
        nsm = NSMClient(settings.NSM_BASE_URL, settings.NSM_TOKEN, debug=debug)
        crm = CRMClient(settings.CRM_BASE_URL, settings.CRM_TOKEN, debug=debug)
        return cls(nsm=nsm, crm=crm, crm_budget=int(getattr(settings, "CRM_LOOKUPS_PER_POLL", 50)))

    # ---- convenience methods ----
    def client_contact_channels(self, client_id: str | int) -> Dict[str, Any]:
        """Return a slim, normalized contact view for a client.

        Shape: {"email": str|"", "phone": str|"", "email_ok": bool, "sms_ok": bool}
        """
        if not self.crm_budget.allow():
            return {"email": "", "phone": "", "email_ok": True, "sms_ok": True}
        c = self.crm.get_client(client_id)
        # tolerate a variety of CRM shapes
        email = c.get("contactEmail") or c.get("email") or ""
        phone = c.get("contactPhone") or c.get("phone") or ""
        # opt-outs could live under flags or tags – default to notify unless opted out
        email_ok = bool(c.get("email_notify", True))
        sms_ok = bool(c.get("sms_notify", True))
        # drill into contacts[] if top-level empty
        if (not email or not phone) and isinstance(c.get("contacts"), list):
            for p in c["contacts"]:
                email = email or p.get("email") or ""
                phone = phone or p.get("phone") or ""
        return {"email": email, "phone": phone, "email_ok": email_ok, "sms_ok": sms_ok}

    def uisp_health_check(hostname: str = "esvc.uisp.com", count: int = 3, timeout_s: float = 1.0) -> dict:
        """
        Return dict with {host, ip, sent, recv, loss_pct}. Uses /bin/ping if available.
        """
        ip = None
        try:
            ip = socket.gethostbyname(hostname)
        except Exception:
            pass

        target = ip or hostname
        sent = count
        recv = 0
        try:
            # Linux/mac ping
            args = ["ping", "-c", str(count), "-W", str(int(timeout_s)), target]
            out = subprocess.run(args, capture_output=True, text=True, timeout=timeout_s * count + 1)
            # Rough parse: count 'bytes from'
            recv = sum(1 for line in (out.stdout or "").splitlines() if "bytes from" in line)
        except Exception:
            # fall back: TCP connect to 443
            for _ in range(count):
                try:
                    with socket.create_connection((target, 443), timeout=timeout_s):
                        recv += 1
                except Exception:
                    pass

        loss = 0 if sent == 0 else int(round(100 * (sent - recv) / sent))
        return {"host": hostname, "ip": ip, "sent": sent, "recv": recv, "loss_pct": loss}

# -----------------------------
# Optional quick CLI (dev only)
# -----------------------------
if __name__ == "__main__":
    import sys

    args = sys.argv[1:]
    dbg = "--debug" in args

    svc = UISPService.from_config(debug=dbg)
    if not args or args[0] in {"-h", "--help"}:
        print(
            """
UISP service demo commands (dev only)
------------------------------------
list-onus <OLT_ID> [--debug]
    Print number of ONUs under an OLT (uses NSM).

client <CLIENT_ID> [--debug]
    Print a compact contact summary (uses CRM).

mk-ticket <CLIENT_ID> <SUBJECT> [NOTE...] [--debug] [--no-dry]
    Create a ticket (returns id).

upd-ticket <ID> [--status <open|pending|closed>] [--note TEXT] [--no-dry] [--debug]
cls-ticket <ID> [--note TEXT] [--no-dry] [--debug]
            """
        )
        sys.exit(0)

    cmd = args[0]
    a = args[1:]

    if cmd == "list-onus" and a:
        onus = svc.nsm.list_onus_by_olt(a[0])
        print(f"ONUs under {a[0]}: {len(onus)}")
        sys.exit(0)

    if cmd == "client" and a:
        info = svc.client_contact_channels(a[0])
        print(json.dumps(info, indent=2))
        sys.exit(0)

    if cmd == "mk-ticket" and len(a) >= 2:
        dry = "--no-dry" not in a
        note = " ".join([x for x in a[2:] if not x.startswith("--")]) or None
        tid = svc.crm.create_ticket(client_id=int(a[0]), subject=a[1], note=note, dry=dry)
        print(f"ticket_id={tid}")
        sys.exit(0)

    if cmd == "upd-ticket" and a:
        dry = "--no-dry" not in a
        tid = a[0]
        status = None
        note = None
        if "--status" in a:
            i = a.index("--status")
            if i + 1 < len(a):
                status = a[i + 1]
        if "--note" in a:
            i = a.index("--note")
            if i + 1 < len(a):
                note = a[i + 1]
        ok = svc.crm.update_ticket(tid, status=status, note=note, dry=dry)
        print("OK" if ok else "FAIL")
        sys.exit(0)

    if cmd == "cls-ticket" and a:
        dry = "--no-dry" not in a
        tid = a[0]
        note = None
        if "--note" in a:
            i = a.index("--note")
            if i + 1 < len(a):
                note = a[i + 1]
        ok = svc.crm.close_ticket(tid, note=note, dry=dry)
        print("OK" if ok else "FAIL")
        sys.exit(0)

    print("unknown command; run with --help")
