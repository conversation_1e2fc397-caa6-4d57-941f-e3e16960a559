# Gandalf WTN Outage Monitor

Monitors specified UISP regions (NSM) for ONUs that are `disconnected` (excluding `Suspended`), enriches with UISP CRM
contact & address, and notifies customers and staff (per policy). Optional: open/close CRM tickets for **minor** (single) outages.

## Quick Start (Standalone, no Celery)

```bash
# 1) Create/activate venv
python -m venv .venv
source .venv/bin/activate   # Windows: .venv\Scripts\activate

# 2) Install deps
pip install -r requirements.txt

# 3) Configure env (see Environment below)
# Minimal:
#   NSM_BASE_URL, NSM_TOKEN
#   CRM_BASE_URL, CRM_TOKEN
#   SMS_STORE_PATH (path to sqlite; ensure folder exists)
# Optional:
#   TEST_MODE=1 to suppress real SMS/email and log instead.

# 4) Run
python run_Local_loop.py

## Quick Start (Celery)

# Broker/backends
export CELERY_BROKER_URL="redis://localhost:6379/0"
export CELERY_RESULT_BACKEND="redis://localhost:6379/1"

# Worker
celery -A celery_app.app worker --loglevel=INFO

# Beat scheduler (if you want periodic polling)
celery -A celery_app.app beat --loglevel=INFO

Environment
UISP (NSM controller)

NSM_BASE_URL — e.g. https://esvc.uisp.com/nms/api/v2.1

NSM_TOKEN — NSM API token

UISP CRM

CRM_BASE_URL — e.g. https://esvc.uisp.com/api/v1.0

CRM_TOKEN — CRM API token

Regions / OLT mapping

Defined in config.Settings.LOCATIONS. Override via env if needed (advanced), otherwise edit config.py.

Polling & thresholds

REGION_KEYS — comma list (default: wtn,ccp,wts,off,wl)

POLL_INTERVAL_SECS — poll cadence (default 30)

PER_DEVICE_NOTIFY_AFTER_MIN — wait X minutes before notifying a single-customer outage

PER_DEVICE_RESTORE_STABLE_MIN — require X minutes stable before sending “restored”

FLAP_SUPPRESS_MIN — suppression window for quick flaps

Cluster (burst) logic

CLUSTER_WINDOW_SECS, CLUSTER_THRESHOLD_N, CLUSTER_URGENT_M, FIRST_ESCALATION_MIN,
CLUSTER_RESTORE_STABLE_MIN, SITE_CRITICAL_MINUTES

Notification preferences

NOTIFY_PREF — sms_first or email_first

Staff mailing lists (single addresses):

MODERATE_MAIL (default <EMAIL>)

URGENT_MAIL (default <EMAIL>)

CRITICAL_MAIL (default <EMAIL>)

Staff SMS lists (comma-separated):

NOC_HD365_SMS

FIELD_SUPERVISOR_SMS

AFTER_HOURS_EMAILS (emails; comma-separated)

SMS / Email

VITELITY_USERNAME, VITELITY_PASSWORD, VITELITY_FROM_NUMBER

SMS_STORE_PATH — sqlite path for de-dupe & rate limiting (ensure directory exists)

Time / office hours

TIMEZONE — e.g. America/New_York

OFFICE_HOURS_START / OFFICE_HOURS_END — HH:MM (local time)

Tickets (CRM)

Tickets are opened only for minor outages (single, not a cascade).

Close on restore (stable).

Uses CRM /ticketing/tickets endpoints via UispClient.

Test / Dry-run modes

TEST_MODE=1 — never hit external SMS/SMTP; all notifications are logged only.

DRY_RUN=1 — skip ticket creates/updates and SMS/email sends, log actions instead.

Logs

Look for detector: offline detail lines to see identification, client ID, and resolved address.

tasks: lines show chosen channel(s), contact preferences, and ticket decisions.