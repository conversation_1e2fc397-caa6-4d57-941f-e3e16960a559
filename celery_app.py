# celery_app.py — drop-in
import os
from datetime import timed<PERSON><PERSON>

from celery import Celery
from celery.schedules import crontab

BROKER_URL  = os.getenv("CELERY_BROKER_URL", "redis://1********:6379/0")
RESULT_BACKEND = os.getenv("CELERY_RESULT_BACKEND", "redis://1********:6379/1")

app = Celery("gandalf", broker=BROKER_URL, backend=RESULT_BACKEND)

# Core config
app.conf.update(
    enable_utc=False,
    timezone="America/New_York",
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    imports=("tasks",),  # ensure worker registers tasks on import
    task_acks_late=True,             # safer re-delivery if worker dies mid-task
    worker_prefetch_multiplier=1,    # reduce burstiness
    task_time_limit=120,             # hard limit (seconds)
    task_soft_time_limit=110,        # soft limit
)

# Periodic schedule (Beat)
app.conf.beat_schedule = {
    # Frequent status poll to drive notifications (no kwargs here)
    "poll-and-notify-90s": {                # this task polls the OLT switches
        "task": "tasks.poll_and_notify",
        "schedule": 60.0,
    },
    "probe-and-notify-10s": {               # this task polls the FS3900 switches
        "task": "tasks.probe_and_notify",
        "schedule": 10.0,
    },
    # Aggregations
    "rollup-5m": {
        "task": "tasks.rollup_5m",
        "schedule": 300.0,
        "args": [],
    },
    "rollup-30m": {
        "task": "tasks.rollup_30m",
        "schedule": 1800.0,
        "args": [],
    },
    # Daily email (08:00 America/New_York)
    "daily-status-midnight": {
        "task": "tasks.daily_status_digest",
        "schedule": crontab(hour=00, minute=1),
    },
    # Retention prune
    "retention-prune-hourly": {
        "task": "tasks.retention_prune",
        "schedule": 3600.0,
        "args": [],
    },
    "nightly-maintenance-0001": {
        "task": "tasks.nightly_maintenance",
        "schedule": crontab(minute=1, hour=0),  # 00:01 local
    },
}
