#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FS3900Probe
-----------
A compact, single-responsibility client for FS3900 switches that:
- discovers per-port SFP identity,
- reads DOM live values,
- fetches DDM alarm thresholds,
- resolves ifIndex and link up/down (ifOperStatus),
- samples traffic (bps) with HC counters and wrap handling,
- classifies each metric against thresholds to normal/caution/alarm,
- returns a structured snapshot suitable for persistence and notifications.

Design goals:
- No hidden globals. No new environment knobs. Call explicitly with args.
- Strict separation of acquisition (SNMP) vs. interpretation (thresholds & severity).
- Robust to partial SNMP failures: returns what it can, never raises on missing OIDs.
- Heavily annotated for maintainability.

Net-SNMP CLI required: snmpwalk, snmpget
"""

from __future__ import annotations

import os
import re
import time
import subprocess
from dataclasses import dataclass, asdict
from typing import Dict, List, Tuple, Optional, Iterable


# ----------------------------- FS3900 MIB Roots ------------------------------

# NOTE: These come directly from your proven ad-hoc tools/scripts and examples.
# We keep them here to avoid hunt-the-OID games elsewhere in the code.

FS_BASE     = "*******.4.1.52642.********"
ID_BASE     = FS_BASE + ".2.10.1"  # SFP identity subtree (strings)
DOM_VAL_BASE= FS_BASE + ".2.11.1"  # DOM live readings (strings with units + state word)
THR_BASE    = FS_BASE + ".2.12.1"  # DDM thresholds (INTEGER, scale=100)

# IF-MIB (standard)
IFNAME      = "*******.********.1.1.1"     # ifName
IFDESCR     = "*******.*******.1.2"         # ifDescr
IFOPER      = "*******.*******.1.8"         # ifOperStatus (1=up,2=down,3=testing,4=unknown,5=dormant,6=notPresent,7=lowerLayerDown)
IFHC_IN     = "*******.********.1.1.6"     # IFHC_IN
IFHC_OUT    = "*******.********.1.1.10"    # IFHC_OUT
IF32_IN     = "*******.*******.1.10"       # IF32_IN
IF32_OUT    = "*******.*******.1.16"       # IF32_OUT

# FS3900 only publishes DOM for ports 25..28 (per your field evidence)
FS3900_DOM_PORTS = (25, 26, 27, 28)
# ------------------------------ Data Structures ------------------------------

@dataclass(frozen=True)
class DomThreshold:
    """Four-sided threshold (low alarm/warn, high warn/alarm)."""
    low_alarm:  Optional[float]
    low_warn:   Optional[float]
    high_warn:  Optional[float]
    high_alarm: Optional[float]

@dataclass(frozen=True)
class PortThresholds:
    """Thresholds for all DOM metrics on a single port."""
    temp_c:  DomThreshold
    vcc_v:   DomThreshold
    bias_ma: DomThreshold
    tx_dbm:  DomThreshold
    rx_dbm:  DomThreshold
    flag:    Optional[int] = None  # raw FS flag if present

@dataclass(frozen=True)
class DomLive:
    """Live DOM values parsed from the FS3900 vendor strings."""
    temp_c:  Optional[float]
    vcc_v:   Optional[float]
    bias_ma: Optional[float]
    tx_dbm:  Optional[float]
    rx_dbm:  Optional[float]
    raw_state: Optional[str]  # trailing state token (e.g., "normal", "alarm")

@dataclass(frozen=True)
class SfpIdentity:
    """SFP identity string fields."""
    connector:     Optional[str]
    fiber:         Optional[str]
    eth_compliance:Optional[str]
    baud:          Optional[str]
    vendor_oui:    Optional[str]
    vendor_name:   Optional[str]
    vendor_pn:     Optional[str]
    vendor_rev:    Optional[str]
    vendor_sn:     Optional[str]
    date_code:     Optional[str]

@dataclass(frozen=True)
class TrafficSample:
    """Traffic sample over an interval."""
    in_bps:  Optional[float]
    out_bps: Optional[float]
    dt_ms:   int

@dataclass(frozen=True)
class PortSnapshot:
    """All we know for one port in a single acquisition cycle."""
    port: int                                # logical FS port number (e.g., 25..28)
    ifindex: Optional[int]                   # resolved ifIndex (or None)
    oper_status: Optional[str]               # 'up','down','dormant', etc., or None if no ifIndex
    identity: Optional[SfpIdentity]          # SFP identity (if read)
    dom: Optional[DomLive]                   # Live DOM values
    thresholds: Optional[PortThresholds]     # Thresholds (if read)
    traffic: Optional[TrafficSample]         # Measured traffic (if sampled)
    metric_severity: Dict[str, str]          # per-metric severity ('normal','caution','alarm','unknown')
    worst_severity: str                      # 'normal'|'caution'|'alarm'|'unknown'

# ------------------------------- FS3900 Client -------------------------------

class FS3900Probe:
    """
    An acquisition & classification façade for a **single** FS3900.

    Typical usage in a task:
        probe = FS3900Probe(host="************", community=os.getenv("SNMP_COMMUNITY","SuperCow"))
        snap  = probe.snapshot(sample_traffic=True, traffic_sleep_s=1.5)

    The `snapshot` result is a dict with metadata and `ports` -> list[PortSnapshot]
    (as dicts), safe to persist to your DB layer or publish to notifications.

    Implementation notes:
      - Uses only net-SNMP CLI tools (no pysnmp) for parity with your environment.
      - Robust parsing: returns None for missing values; never throws on SNMP misses.
      - Classifies metric severities relative to per-port thresholds if available.
    """

    # --------------- construction / static config ----------------

    def __init__(
        self,
        host: str,
        community: str,
        *,
        ports: Iterable[int] = FS3900_DOM_PORTS,
        timeout_s: float = 0.6,
        retries: int = 1,
    ) -> None:
        self.host       = host
        self.community  = community
        self.timeout_s  = timeout_s
        self.retries    = retries
        self.ports      = tuple(int(p) for p in ports)  # immutable tuple
        # precompile parsing regex
        self._re_float  = re.compile(r"(-?\d+(?:\.\d+)?)")

    # ------------------------------ public API ------------------------------

    def snapshot(self, *, sample_traffic: bool = True, traffic_sleep_s: float = 1.5) -> Dict:
        """
        Full acquisition pass + classification.

        Returns a serializable dict:
            {
              "host": "...",
              "t": <epoch seconds>,
              "ports": [ {PortSnapshot as dict}, ... ]
            }
        """
        t0 = time.time()

        # 1) IF-MIB maps (names and descriptions) -> ifIndex:label
        if_map = self._build_ifindex_map()

        # 2) Resolve {port -> ifIndex} heuristically from ifName/ifDescr
        ifindex_by_port = self._resolve_ports_to_ifindex(if_map)

        # 3) SFP identity, live DOM, thresholds (FS private MIB)
        identity_by_port   = self._collect_sfp_identity()
        dom_by_port        = self._collect_dom_live()
        thresholds_by_port = self._collect_thresholds()

        # 4) ifOperStatus by ifIndex
        oper_by_ifindex = self._get_oper_status_bulk(set(ifindex_by_port.values()))

        # 5) Optional traffic sampling
        traffic_by_ifindex: Dict[int, TrafficSample] = {}
        if sample_traffic:
            for p in self.ports:
                ifidx = ifindex_by_port.get(p)
                if ifidx is None:
                    continue
                in_bps, out_bps, dt_ms = self._measure_bps(ifidx, sleep_s=traffic_sleep_s)
                traffic_by_ifindex[ifidx] = TrafficSample(in_bps, out_bps, dt_ms)

        # 6) Classification per metric + worst per port
        out: List[Dict] = []
        for port in self.ports:
            ifidx = ifindex_by_port.get(port)
            oper  = self._normalize_oper(oper_by_ifindex.get(ifidx)) if ifidx else None

            ident = identity_by_port.get(port)
            dom   = dom_by_port.get(port)
            th    = thresholds_by_port.get(port)
            traffic = traffic_by_ifindex.get(ifidx) if ifidx else None

            metric_sev = self._classify_all(dom, th)
            worst = self._worst_severity(metric_sev.values())

            ps = PortSnapshot(
                port=port,
                ifindex=ifidx,
                oper_status=oper,
                identity=ident,
                dom=dom,
                thresholds=th,
                traffic=traffic,
                metric_severity=metric_sev,
                worst_severity=worst,
            )
            out.append(asdict(ps))

        elapsed = round(time.time() - t0, 3)
        return {"host": self.host, "t": int(t0), "elapsed_s": elapsed, "ports": out}

    # ------------------------------ Public Helpers for tasks.py ------------------

    def get_ifindex(self, ifname: str) -> Optional[int]:
        """
        Resolve 'ethernet 1/N' -> ifIndex N deterministically.
        If format differs, fall back to the existing map/normalize pass.
        """
        if not ifname:
            return None
        m = re.search(r"\b1\s*/\s*(\d+)\b", ifname, re.I)
        if m:
            return int(m.group(1))

        # fallback: use the already-implemented builder
        try:
            if_map = self._build_ifindex_map()  # {ifIndex:int -> label:str}
        except Exception:
            return None

        tgt = (ifname or "").strip().lower()
        tgt_ns = "".join(tgt.split())  # "ethernet1/19" normalization
        for idx, lbl in (if_map or {}).items():
            s = (lbl or "").strip().lower()
            if s == tgt:
                return idx
            if "".join(s.split()) == tgt_ns:
                return idx
        return None

    def read_oper_status_multi(self, ifindexes: list[int]) -> dict[int, int | None]:
        """
        Batch-read IF-MIB::ifOperStatus for many ports at once.
        Returns {ifindex: int_or_None}
        """
        if not ifindexes:
            return {}
        idxs = [int(i) for i in ifindexes]
        oids = [f"{IFOPER}.{i}" for i in idxs]
        res = self._snmpget_many(oids) or {}

        def _parse_int(s: str | None) -> int | None:
            if not s:
                return None
            try:
                # Accept "INTEGER: 1" or plain "1"
                v = s.split(":", 1)[1].strip() if ":" in s else s.strip().split()[-1]
                return int(v)
            except Exception:
                return None

        out: dict[int, int | None] = {}
        for i in idxs:
            out[i] = _parse_int(res.get(f"{IFOPER}.{i}"))
        return out

    def _counter_oid_pairs(self, ifindex: int) -> tuple[tuple[str, str], tuple[str, str]]:
        """Return ((HC_in, HC_out), (IN32, OUT32)) for an ifIndex."""
        return (
            (f"{IFHC_IN}.{ifindex}", f"{IFHC_OUT}.{ifindex}"),
            (f"{IF32_IN}.{ifindex}", f"{IF32_OUT}.{ifindex}"),
        )

    def _try_get_counters_once(self, ifindex: int, use_hc: bool) -> tuple[int | None, int | None]:
        """One batched GET for in/out octets. Returns (inOctets, outOctets) or (None, None)."""
        (hc_in, hc_out), (in32, out32) = self._counter_oid_pairs(ifindex)
        oids = [hc_in, hc_out] if use_hc else [in32, out32]
        got = self._snmpget_many(oids)  # <-- single round-trip
        if not got or len(got) < 2:
            return (None, None)
        vals = list(got.values())

        def _to_int(x: str) -> int | None:
            # Accept forms like "Counter64: 123", "123", "INTEGER: 123"
            try:
                tail = x.split(":")[-1].strip()
                return int(tail)
            except Exception:
                # last token best-effort
                try:
                    return int(x.strip().split()[-1])
                except Exception:
                    return None

        return (_to_int(vals[0]), _to_int(vals[1]))

    def _measure_bps(self, ifindex: int, *, sleep_s: float = 1.5) -> tuple[float | None, float | None, int]:
        """
        Batched, HC-first sampler:
          - GET in/out (HC)   -> sleep -> GET in/out (HC)
          - if HC fails once, fall back to 32-bit for this interface thereafter
          - wrap-safe for 32-bit
          - returns (in_bps, out_bps, dt_ms)
        """
        if not hasattr(self, "_hc_ok"):
            self._hc_ok = {}  # type: dict[int, bool]

        use_hc = self._hc_ok.get(ifindex, True)

        # First read
        t0 = time.time()
        in1, out1 = self._try_get_counters_once(ifindex, use_hc)
        # If HC was requested but missing, immediately fall back to 32-bit
        if use_hc and (in1 is None or out1 is None):
            use_hc = False
            self._hc_ok[ifindex] = False
            in1, out1 = self._try_get_counters_once(ifindex, use_hc)

        if in1 is None or out1 is None:
            return (None, None, 0)

        time.sleep(sleep_s)

        # Second read
        t1 = time.time()
        in2, out2 = self._try_get_counters_once(ifindex, use_hc)
        if in2 is None or out2 is None:
            return (None, None, 0)

        dt = max(t1 - t0, 1e-6)  # seconds
        dt_ms = int(round(dt * 1000))

        # Delta with wrap handling
        if use_hc:
            maxv = 2 ** 64
        else:
            maxv = 2 ** 32

        def _delta(a: int, b: int) -> int:
            return (b - a) if b >= a else (b + (maxv - a))

        din = _delta(in1, in2)
        dout = _delta(out1, out2)

        in_bps = (din * 8.0) / dt
        out_bps = (dout * 8.0) / dt

        # First success with HC => remember
        if ifindex not in self._hc_ok:
            self._hc_ok[ifindex] = use_hc

        return (max(in_bps, 0.0), max(out_bps, 0.0), dt_ms)

    # Public wrapper stays the same signature your task already calls:
    def sample_bps(self, ifindex: int, *, sleep_s: float = 1.0) -> tuple[float | None, float | None, int]:
        return self._measure_bps(ifindex, sleep_s=sleep_s)

    def read_ddm_thresholds(self, ports: Iterable[int] = (25, 26, 27, 28)) -> dict[int, dict]:
        """
        Return just the RX thresholds for given ports:
           {port: {"rx_dbm": {"low_alarm", "low_warn", "high_warn", "high_alarm"}}}
        """
        thr_map = self._collect_thresholds()  # existing private method
        out: dict[int, dict] = {}
        want = set(int(p) for p in ports)
        for p, th in thr_map.items():
            if p in want and getattr(th, "rx_dbm", None):
                rx = th.rx_dbm
                out[p] = {"rx_dbm": {
                    "low_alarm": rx.low_alarm,
                    "low_warn": rx.low_warn,
                    "high_warn": rx.high_warn,
                    "high_alarm": rx.high_alarm,
                }}
        return out

    def sample_bps_multi(self, ifindexes: list[int], *, sleep_s: float = 1.0) -> dict[int, tuple[float, float, int]]:
        """
        Measure in/out bps for multiple ports in one shot:
          - Try 64-bit HC counters first per port; fall back to 32-bit if missing.
          - One sleep for all ports.
        Returns: {ifindex: (in_bps, out_bps, dt_ms)}
        """
        if not ifindexes:
            return {}
        idxs = [int(i) for i in ifindexes]

        def _mk_oids(use_hc: bool) -> list[str]:
            oids = []
            if use_hc:
                for i in idxs:
                    oids.append(f"{IFHC_IN}.{i}")
                    oids.append(f"{IFHC_OUT}.{i}")
            else:
                for i in idxs:
                    oids.append(f"{IF32_IN}.{i}")
                    oids.append(f"{IF32_OUT}.{i}")
            return oids

        def _parse_val(s: str) -> int | None:
            # Accept "Counter64: 123" or "123"
            try:
                if ":" in s:
                    return int(s.split(":", 1)[1].strip())
                tail = s.strip().split()[-1]
                return int(tail)
            except Exception:
                return None

        # First read: prefer HC
        use_hc = True
        t0 = time.time()
        res1 = self._snmpget_many(_mk_oids(True)) or {}
        # Identify any ports that failed HC read
        missing_hc = set()
        for i in idxs:
            if f"{IFHC_IN}.{i}" not in res1 or f"{IFHC_OUT}.{i}" not in res1:
                missing_hc.add(i)

        # Fallback for missing HC
        if missing_hc:
            res1_fallback = self._snmpget_many([f"{IF32_IN}.{i}" for i in missing_hc] +
                                               [f"{IF32_OUT}.{i}" for i in missing_hc]) or {}
            res1.update(res1_fallback)

        time.sleep(sleep_s)
        t1 = time.time()
        res2 = self._snmpget_many(_mk_oids(True)) or {}
        if missing_hc:
            res2_fallback = self._snmpget_many([f"{IF32_IN}.{i}" for i in missing_hc] +
                                               [f"{IF32_OUT}.{i}" for i in missing_hc]) or {}
            res2.update(res2_fallback)

        dt = max(t1 - t0, 1e-6)
        dt_ms = int(round(dt * 1000))

        out: dict[int, tuple[float, float, int]] = {}
        for i in idxs:
            # decide width per port
            hc_ok = (f"{IFHC_IN}.{i}" in res1 and f"{IFHC_OUT}.{i}" in res1 and
                     f"{IFHC_IN}.{i}" in res2 and f"{IFHC_OUT}.{i}" in res2)
            in_oid1 = f"{IFHC_IN}.{i}" if hc_ok else f"{IF32_IN}.{i}"
            out_oid1 = f"{IFHC_OUT}.{i}" if hc_ok else f"{IF32_OUT}.{i}"
            in1 = _parse_val(res1.get(in_oid1, ""))
            out1 = _parse_val(res1.get(out_oid1, ""))
            in2 = _parse_val(res2.get(in_oid1, ""))
            out2 = _parse_val(res2.get(out_oid1, ""))
            if in1 is None or out1 is None or in2 is None or out2 is None:
                out[i] = (0.0, 0.0, 0)
                continue

            maxv = 2 ** 64 if hc_ok else 2 ** 32

            def _delta(a, b):
                return (b - a) if b >= a else (b + (maxv - a))

            din, dout = _delta(in1, in2), _delta(out1, out2)
            in_bps = max((din * 8.0) / dt, 0.0)
            out_bps = max((dout * 8.0) / dt, 0.0)
            out[i] = (in_bps, out_bps, dt_ms)

        return out

    # ---------------------------- classification ----------------------------

    @staticmethod
    def _severity_rank(v: str) -> int:
        order = {"normal": 0, "caution": 1, "alarm": 2, "unknown": -1}
        return order.get(v, -1)

    def _worst_severity(self, vals: Iterable[str]) -> str:
        worst = "normal"
        maxrank = -1
        for v in vals:
            r = self._severity_rank(v)
            if r > maxrank:
                worst, maxrank = v, r
        return worst if maxrank >= 0 else "unknown"

    def _classify_all(
        self,
        dom: Optional[DomLive],
        th:  Optional[PortThresholds],
    ) -> Dict[str, str]:
        """
        Map each metric to 'normal'|'caution'|'alarm'|'unknown'.
        Directionality rules:
          - temp_c: high is bad; low sides also defined (use both).
          - vcc_v: low is bad; high also bad if above high_warn/alarm.
          - bias_ma: both low and high sides defined; treat like temp.
          - tx_dbm, rx_dbm: both low and high sides defined.
        """
        metrics = ("temp_c","vcc_v","bias_ma","tx_dbm","rx_dbm")
        if dom is None or th is None:
            return {m:"unknown" for m in metrics}

        d = {
            "temp_c": dom.temp_c,
            "vcc_v":  dom.vcc_v,
            "bias_ma":dom.bias_ma,
            "tx_dbm": dom.tx_dbm,
            "rx_dbm": dom.rx_dbm,
        }
        t = {
            "temp_c": th.temp_c,
            "vcc_v":  th.vcc_v,
            "bias_ma":th.bias_ma,
            "tx_dbm": th.tx_dbm,
            "rx_dbm": th.rx_dbm,
        }
        out: Dict[str,str] = {}
        for name in metrics:
            out[name] = self._classify_value(d[name], t[name])
        return out

    @staticmethod
    def _classify_value(value: Optional[float], thr: DomThreshold) -> str:
        """
        Two-sided band classification relative to thresholds:
            low_alarm  < low_warn  <  (OK band)  < high_warn < high_alarm
        Any value ≤ low_alarm or ≥ high_alarm -> 'alarm'
        Any value between (low_alarm, low_warn] or [high_warn, high_alarm) -> 'caution'
        Missing value or thresholds -> 'unknown'
        """
        if value is None:
            return "unknown"
        lo_a, lo_w, hi_w, hi_a = thr.low_alarm, thr.low_warn, thr.high_warn, thr.high_alarm
        # If we lack any relevant threshold, fall back gracefully.
        if lo_a is None and lo_w is None and hi_w is None and hi_a is None:
            return "unknown"

        # Low side
        if lo_a is not None and value <= lo_a:
            return "alarm"
        if lo_w is not None and value <= lo_w:
            return "caution"

        # High side
        if hi_a is not None and value >= hi_a:
            return "alarm"
        if hi_w is not None and value >= hi_w:
            return "caution"

        return "normal"

    # ------------------------------- SNMP IO ---------------------------------

    def _run(self, argv: List[str]) -> Tuple[int, str, str]:
        """
        Spawn a command and capture all output. We *never* print the community.
        """
        p = subprocess.Popen(argv, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        out, err = p.communicate()
        return p.returncode, (out or ""), (err or "")

    def _snmpwalk(self, oid: str) -> List[str]:
        """
        Return snmpwalk output lines (trimmed), or [] on failure.
        """
        cmd = [
            "snmpwalk", "-v2c", "-c", self.community, "-On",
            "-t", str(self.timeout_s), "-r", str(self.retries), self.host, oid
        ]
        rc, out, _ = self._run(cmd)
        if rc != 0:
            return []
        return [ln.strip() for ln in out.splitlines() if ln.strip()]

    def _snmpget_many(self, oids: List[str]) -> Dict[str, str]:
        if not oids:
            return {}
        cmd = ["snmpget", "-v2c", "-c", self.community, "-On",
               "-t", str(self.timeout_s), "-r", str(self.retries), self.host] + oids
        rc, out, _ = self._run(cmd)
        lines = [ln.strip() for ln in out.splitlines() if ln.strip()]
        res: Dict[str, str] = {}
        for ln in lines:
            try:
                k, v = ln.split(" = ", 1)
                k = k.strip()
                if k.startswith("."):  # <-- add this normalization
                    k = k[1:]
                res[k] = v.strip()
            except Exception:
                continue
        return res

    # ------------------------------ IF-MIB bits ------------------------------

    def _parse_if_index_and_value(self, lines: List[str]) -> Dict[int, str]:
        """
        Parse snmpwalk output like:
          .1.3.6....1.<ifIndex> = STRING: "Port26"
        into {ifIndex: "Port26"}.
        """
        res: Dict[int, str] = {}
        for ln in lines:
            try:
                left, right = ln.split(" = ", 1)
                idx = int(left.strip().split(".")[-1])
                after_colon = right.split(":", 1)[1].strip()
                if after_colon.startswith('"') and after_colon.endswith('"'):
                    after_colon = after_colon[1:-1]
                res[idx] = after_colon
            except Exception:
                continue
        return res

    def _build_ifindex_map(self) -> Dict[int, str]:
        """
        Build a consolidated {ifIndex: label} map, preferring ifName over ifDescr.
        On FS3900, ifName tends to be concise (PortN); ifDescr longer but informative.
        """
        names = self._parse_if_index_and_value(self._snmpwalk(IFNAME))
        descr = self._parse_if_index_and_value(self._snmpwalk(IFDESCR))
        out = dict(descr)
        for k, v in names.items():
            if v:
                out[k] = v
        return out

    def _resolve_ports_to_ifindex(self, if_map: Dict[int, str]) -> Dict[int, int]:
        """
        Heuristics to map FS logical ports (25..28) to ifIndex, matching common
        vendor strings: "ethernet 1/26", "Port26", "... unit 1, port 26", etc.
        """
        res: Dict[int, int] = {}
        # Pre-compile patterns per port for readability; allow both with/without spaces.
        cache: Dict[int, List[re.Pattern]] = {}
        def pats_for(p: int) -> List[re.Pattern]:
            if p in cache:
                return cache[p]
            cache[p] = [
                re.compile(rf"\bethernet\s*1/{p}\b", re.I),
                re.compile(rf"\bport\s*{p}\b", re.I),              # "Port26" or "Port 26"
                re.compile(rf"\bunit\s*1,\s*port\s*{p}\b", re.I),  # "... unit 1, port 26"
                re.compile(rf"\b1/{p}\b", re.I),
            ]
            return cache[p]

        for ifidx, label in if_map.items():
            s = (label or "").lower()
            for p in self.ports:
                for pat in pats_for(p):
                    if pat.search(s):
                        res.setdefault(p, ifidx)
                        break
        return res

    def _get_oper_status_bulk(self, ifindices: Iterable[int]) -> Dict[int, int]:
        """
        Return {ifIndex: raw ifOperStatus integer}, missing keys if snmpget fails.
        ifOperStatus values (RFC 2863): 1 up, 2 down, 3 testing, 4 unknown,
                                       5 dormant, 6 notPresent, 7 lowerLayerDown
        """
        oid_list = [f"{IFOPER}.{i}" for i in sorted(ifindices)]
        raw = self._snmpget_many(oid_list)
        out: Dict[int,int] = {}
        for oid, line in raw.items():
            try:
                idx = int(oid.split(".")[-1])
                # Examples: "INTEGER: 1" or "INTEGER: up(1)"; last token is safe
                last = line.split()[-1]
                num = int(last.split(")")[0].split("(")[-1]) if "(" in last else int(last)
                out[idx] = num
            except Exception:
                continue
        return out

    @staticmethod
    def _normalize_oper(val: Optional[int]) -> Optional[str]:
        """Map ifOperStatus integer to string."""
        if val is None:
            return None
        return {
            1: "up",
            2: "down",
            3: "testing",
            4: "unknown",
            5: "dormant",
            6: "notPresent",
            7: "lowerLayerDown",
        }.get(val, "unknown")

    # ---------------------------- Traffic sampling ---------------------------

    def _snmpget_counter(self, oid: str) -> Optional[int]:
        """
        Read a counter (Counter64/Counter32) as integer. Returns None if absent.
        We use -Oqv to get value-only when possible; if the agent replies with
        typed output, we still extract the last token as an int.
        """
        try:
            out = subprocess.check_output(
                ["snmpget", "-v2c", "-c", self.community, "-On", "-Oqv", self.host, oid],
                stderr=subprocess.DEVNULL,
                timeout=self.timeout_s,
            ).decode().strip()
        except Exception:
            return None
        if not out:
            return None
        try:
            return int(out)
        except ValueError:
            # fallback: "Counter64: 1234" -> take last token
            try:
                return int(out.split()[-1])
            except Exception:
                return None

    def _read_octets_pair(self, ifindex: int) -> Tuple[Optional[int], Optional[int], bool]:
        """
        Returns (in_octets, out_octets, used_64bit).
        Try HC (64-bit), then fall back to 32-bit.
        """
        i64 = self._snmpget_counter(f"{IFHC_IN}.{ifindex}")
        o64 = self._snmpget_counter(f"{IFHC_OUT}.{ifindex}")
        if i64 is not None and o64 is not None:
            return i64, o64, True
        i32 = self._snmpget_counter(f"{IF32_IN}.{ifindex}")
        o32 = self._snmpget_counter(f"{IF32_OUT}.{ifindex}")
        return i32, o32, False

    # ---------------------- FS private MIB: identity/DOM/THR -----------------

    def _parse_oid_tail_index(self, line: str) -> Optional[Tuple[str, int]]:
        """
        Given a line like:
          .*******.4.1.52642.********.********.26 = STRING: "LC"
        return (field_id, port) -> ("2", 26)
        """
        try:
            left, _ = line.split(" = ", 1)
            parts = left.strip().split(".")
            port = int(parts[-1])
            field = parts[-2]
            return (field, port)
        except Exception:
            return None

    def _parse_string_value(self, line: str) -> Optional[str]:
        try:
            _, right = line.split(" = ", 1)
            val = right.split(":", 1)[1].strip()
            if val.startswith('"') and val.endswith('"'):
                val = val[1:-1]
            return val
        except Exception:
            return None

    def _parse_first_float(self, text: Optional[str]) -> Optional[float]:
        if not text:
            return None
        m = self._re_float.search(text)
        return float(m.group(1)) if m else None

    def _collect_sfp_identity(self) -> Dict[int, SfpIdentity]:
        """
        Walk identity subtree; return {port: SfpIdentity}
        Field mapping (under .2.10.1):
            2 connector, 3 fiber, 4 compliance, 5 baud,
            6 OUI, 7 vendor name, 8 PN, 9 rev, 10 SN, 11 date
        """
        lines = self._snmpwalk(ID_BASE)
        field_map = {
            "2":  "connector",
            "3":  "fiber",
            "4":  "eth_compliance",
            "5":  "baud",
            "6":  "vendor_oui",
            "7":  "vendor_name",
            "8":  "vendor_pn",
            "9":  "vendor_rev",
            "10": "vendor_sn",
            "11": "date_code",
        }
        tmp: Dict[int, Dict[str,str]] = {}
        for ln in lines:
            key = self._parse_oid_tail_index(ln)
            if not key:
                continue
            field, port = key
            if port not in self.ports or field not in field_map:
                continue
            val = self._parse_string_value(ln)
            tmp.setdefault(port, {})[field_map[field]] = val or None

        out: Dict[int,SfpIdentity] = {}
        for p, d in tmp.items():
            out[p] = SfpIdentity(**{k: d.get(k) for k in field_map.values()})
        return out

    def _collect_dom_live(self) -> Dict[int, DomLive]:
        """
        Walk DOM live subtree; return {port: DomLive}
        Field mapping (under .2.11.1):
            2 temp, 3 vcc, 4 bias, 5 tx, 6 rx
        """
        lines = self._snmpwalk(DOM_VAL_BASE)
        field_map = {
            "2": ("temp_c", self._parse_first_float),
            "3": ("vcc_v",  self._parse_first_float),
            "4": ("bias_ma",self._parse_first_float),
            "5": ("tx_dbm", self._parse_first_float),
            "6": ("rx_dbm", self._parse_first_float),
        }
        tmp: Dict[int, Dict[str, Optional[float]]] = {}
        raw_state: Dict[int, Optional[str]] = {}
        for ln in lines:
            key = self._parse_oid_tail_index(ln)
            if not key:
                continue
            field, port = key
            if port not in self.ports or field not in field_map:
                continue
            raw = self._parse_string_value(ln) or ""
            name, parser = field_map[field]
            val = parser(raw)
            tmp.setdefault(port, {})[name] = val
            # capture trailing token as state word (e.g., "normal")
            toks = raw.split()
            raw_state[port] = (toks[-1].lower() if toks else None)

        out: Dict[int,DomLive] = {}
        for p, d in tmp.items():
            out[p] = DomLive(
                temp_c=d.get("temp_c"),
                vcc_v=d.get("vcc_v"),
                bias_ma=d.get("bias_ma"),
                tx_dbm=d.get("tx_dbm"),
                rx_dbm=d.get("rx_dbm"),
                raw_state=raw_state.get(p),
            )
        return out

    def _collect_thresholds(self) -> Dict[int, PortThresholds]:
        """
        Walk thresholds subtree; convert scale=100 integers to real units.
        Field ranges (under .2.12.1), per your captures:
            2..5  : temp  (low_alarm, low_warn, high_warn, high_alarm)
            6..9  : vcc
            10..13: bias
            14..17: tx
            18..21: rx
            22    : flag (keep raw int)
        """
        def parse_int(line: str) -> Optional[int]:
            try:
                _, right = line.split(" = ", 1)
                after = right.split(":", 1)[1]
                return int(after.strip())
            except Exception:
                return None

        lines = self._snmpwalk(THR_BASE)
        raw: Dict[Tuple[str,int], int] = {}
        for ln in lines:
            key = self._parse_oid_tail_index(ln)
            if not key:
                continue
            field, port = key
            if port not in self.ports:
                continue
            ival = parse_int(ln)
            if ival is None:
                continue
            raw[(field, port)] = ival

        def get_float(port: int, fid: str) -> Optional[float]:
            v = raw.get((fid, port))
            return (v / 100.0) if v is not None else None

        out: Dict[int,PortThresholds] = {}
        for p in self.ports:
            th = PortThresholds(
                temp_c = DomThreshold(get_float(p,"2"),  get_float(p,"3"),  get_float(p,"4"),  get_float(p,"5")),
                vcc_v  = DomThreshold(get_float(p,"6"),  get_float(p,"7"),  get_float(p,"8"),  get_float(p,"9")),
                bias_ma= DomThreshold(get_float(p,"10"), get_float(p,"11"), get_float(p,"12"), get_float(p,"13")),
                tx_dbm = DomThreshold(get_float(p,"14"), get_float(p,"15"), get_float(p,"16"), get_float(p,"17")),
                rx_dbm = DomThreshold(get_float(p,"18"), get_float(p,"19"), get_float(p,"20"), get_float(p,"21")),
                flag   = raw.get(("22", p)),
            )
            out[p] = th
        return out
