# probe_and_notify.py — lean orchestration + rich messaging + FS3900 hooks
# ======================================================================
# What’s in here (and where to tweak)
# -----------------------------------
# - Config expectations: from config.settings (NO drift)
# - Service layer: UISPService(NSMClient, CRMClient) imported from uisp_service.py
# - Messaging: uses notify.py (your existing sender) with friendly customer copy
#              and staff/NOC summaries (with optional FS3900 health)
# - Orchestrator: one pass -> update state -> open/update/close -> notify
# - Cluster: sliding window; suppressed if UISP host is unreachable
# - Journal: daily_outages.jsonl append on recovery >= MIN_OUTAGE_FOR_JOURNAL_S
# - Locks: file lock to prevent overlapping watchers
#
# Developer notes (pin these)
# ---------------------------
# - Truth over vibes: offline = endpoint.status == 'disconnected' AND suspended == False
#   Suspended is *not* an outage; we can show it (toggle) but don’t escalate.
# - We never put raw GUIDs in customer messages; staff/NOC get humanized context.
# - Dry-run is opt-in: use --no-dry to actually create/patch tickets + send messages.
# - Escalations: during cluster windows, suppress per-customer ticket spam.
# - FS3900: optional. If settings.FS3900_BY_OLT has an entry for an OLT, we probe it.
# - Health sanity: if UISP host is not reachable, do not raise clusters (prevents false wall-of-fire).
#
# Helpers inventory (for future pruning)
# --------------------------------------
# _ping_host_icmp                  → very small ping helper
# _extract_host                    → parse host from NSM_BASE_URL (for ping)
# _fmt_s                           → seconds → 'XmYYs' or 'HhMMm'
# Customer/Staff template builders → _build_customer_* / _build_staff_*
# _fs3900_quick_health             → small wrapper to FS3900Probe.snapshot()
#
# Change log (dev breadcrumbs)
# ----------------------------
# - This file depends on uisp_service.py (NSMClient, CRMClient, UISPService)
# - notify.py is reused as-is (no name drift). We only build strings here.
# - Cluster thresholds read from settings when present, otherwise sane defaults.
# - CRM create/update/close use your working paths from UISPService.CRMClient.
#

from __future__ import annotations

import json
import os
import sys
import time
import socket
import subprocess
import threading
from dataclasses import dataclass, asdict
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timezone

# Canonical config
from config import settings  # type: ignore

# Service + clients (your file)
from uisp_service import NSMClient, CRMClient, UISPService  # type: ignore

# Your messenger (email/sms); we do formatting here, sending in notify.py
import notify  # type: ignore

# Optional FS3900 probe (SNMP + DOM), only used when configured
try:
    from fs3900_probe import FS3900Probe  # type: ignore
except Exception:
    FS3900Probe = None  # type: ignore


# ---------------------------
# Constants / tunables
# ---------------------------
OPEN_TICKET_AFTER_S = 5 * 60         # open ticket after 5 minutes offline
CLOSE_AFTER_RECOVERY_S = 5 * 60      # close ticket 5 minutes after recovery
MIN_OUTAGE_FOR_JOURNAL_S = 2 * 60    # ignore micro-bounces in daily journal

# Cluster thresholds (can be overridden in config.settings)
CLUSTER_WINDOW_SEC = int(getattr(settings, "CLUSTER_WINDOW_SEC", 180))          # 3 minutes window
CLUSTER_MINOR_N = int(getattr(settings, "CLUSTER_MINOR_N", 5))                  # 5 endpoints (tight)
CLUSTER_MODERATE_N = int(getattr(settings, "CLUSTER_MODERATE_N", 10))           # 10 endpoints (tight)
CLUSTER_MAX_OLT_GAP_SEC = int(getattr(settings, "CLUSTER_MAX_OLT_GAP_SEC", 120))
# Cluster persistence (state + journal)
CLUSTER_STATE_PATH = str(getattr(settings, "CLUSTER_STATE_PATH", "./.state/cluster.json"))
CLUSTER_JOURNAL_PATH = str(getattr(settings, "CLUSTER_JOURNAL_PATH", "./cluster_events.jsonl"))

# Cluster “recover” stability (how long below threshold before we close the episode)
CLUSTER_RESTORE_STABLE_S = int(getattr(settings, "CLUSTER_RESTORE_STABLE_S", 180))

# Paths (state/journal/lock) — can be overridden in config
STATE_PATH = str(getattr(settings, "STATE_PATH", "./.state/probe.json"))
JOURNAL_PATH = str(getattr(settings, "JOURNAL_PATH", "./daily_outages.jsonl"))
LOCK_PATH = str(getattr(settings, "LOCK_PATH", "./.state/probe.lock"))

# Toggle: include suspended rows in output
DEFAULT_INCLUDE_SUSPENDED = True


# ---------------------------
# Small utilities
# ---------------------------

def _fmt_s(sec: Optional[float]) -> str:
    if sec is None:
        return ""
    s = int(max(0, sec))
    m, ss = divmod(s, 60)
    h, mm = divmod(m, 60)
    return f"{h}h{mm:02d}m" if h else f"{mm}m{ss:02d}s"


def _iso_utc(ts: float | None) -> str:
    if not ts:
        return ""
    return datetime.fromtimestamp(ts, tz=timezone.utc).strftime("%Y-%m-%d %H:%M:%SZ")


def _extract_host(url: str) -> str:
    # very lenient host extraction
    try:
        no_proto = url.split("://", 1)[-1]
        host = no_proto.split("/", 1)[0]
        return host
    except Exception:
        return url


def _ping_host_icmp(host: str, *, count: int = 2, timeout_s: float = 0.8) -> bool:
    """Cheap reachability ping. Cross-platform best effort."""
    if not host:
        return False
    try:
        # Prefer raw IP; resolve if needed (best effort)
        try:
            ip = socket.gethostbyname(host)
            target = ip
        except Exception:
            target = host
        # Windows vs *nix
        if os.name == "nt":
            cmd = ["ping", "-n", str(count), "-w", str(int(timeout_s * 1000)), target]
        else:
            cmd = ["ping", "-c", str(count), "-W", str(int(timeout_s)), target]
        p = subprocess.run(cmd, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        return p.returncode == 0
    except Exception:
        return False

def _endpoint_site_id_from_onu(dev: dict) -> str:
    """
    Return the *endpoint* id that lives under a region site.
    UISP encodes this in a few places; prefer identification.site.id.
    """
    ident = dev.get("identification") or {}
    sid = ((ident.get("site") or {}).get("id")
           or dev.get("siteId")
           or (dev.get("site") or {}).get("id")
           or "")
    return str(sid or "").strip()

# ---------------------------
# State persistence
# ---------------------------

@dataclass
class DeviceState:
    device_id: str
    olt_id: str
    site_id: str
    client_id: Optional[str] = None
    client_email: Optional[str] = None
    client_phone: Optional[str] = None
    client_email_ok: bool = True
    client_sms_ok: bool = True
    suspended: bool = False

    first_offline_at: Optional[float] = None
    last_seen_poll: Optional[float] = None
    recovered_at: Optional[float] = None
    had_ticket: bool = False
    ticket_id: Optional[str] = None
    sim_ignore_until: Optional[float] = None  # used by simulate recover

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

    @classmethod
    def from_dict(cls, d: Dict[str, Any]) -> "DeviceState":
        return cls(**d)


class OutageState:
    def __init__(self, path: str):
        self.path = path
        self._data: Dict[str, DeviceState] = {}
        os.makedirs(os.path.dirname(self.path) or ".", exist_ok=True)
        self._load()

    def _load(self) -> None:
        if not os.path.exists(self.path):
            self._data = {}
            return
        try:
            with open(self.path, "r", encoding="utf-8") as f:
                raw = json.load(f) or {}
            self._data = {k: DeviceState.from_dict(v) for k, v in raw.items()}
        except Exception:
            self._data = {}

    def save(self) -> None:
        tmp = {k: v.to_dict() for k, v in self._data.items()}
        with open(self.path, "w", encoding="utf-8") as f:
            json.dump(tmp, f, indent=2, sort_keys=True)

    def get(self, device_id: str) -> Optional[DeviceState]:
        return self._data.get(device_id)

    def set(self, st: DeviceState) -> None:
        self._data[st.device_id] = st

    def remove(self, device_id: str) -> None:
        self._data.pop(device_id, None)

    def items(self):
        return self._data.items()

    def keys(self):
        return self._data.keys()

class ClusterState:
    """
    Tracks cluster episodes per (region, olt_id):
      - open when count >= CLUSTER_MINOR_N
      - update peak_count & last_seen
      - close when absent or stale for CLUSTER_RESTORE_STABLE_S
    Persists active state and appends resolved episodes to CLUSTER_JOURNAL_PATH.
    """
    def __init__(self, path: str, journal_path: str):
        self.path = path
        self.journal_path = journal_path
        self.active: Dict[str, Dict[str, Any]] = {}
        os.makedirs(os.path.dirname(self.path) or ".", exist_ok=True)
        self._load()

    def _load(self) -> None:
        try:
            with open(self.path, "r", encoding="utf-8") as f:
                self.active = json.load(f) or {}
        except Exception:
            self.active = {}

    def save(self) -> None:
        try:
            with open(self.path, "w", encoding="utf-8") as f:
                json.dump(self.active, f)
        except Exception:
            pass

    @staticmethod
    def _key(region: str, olt_id: str) -> str:
        return f"{region}|{olt_id}"

    def update_from_classification(self, cls: Dict[Tuple[str, str], Dict[str, Any]], now: float) -> None:
        # open or update any clusters at/above minor threshold
        for (region, olt_id), rec in cls.items():
            count = int(rec.get("count", 0))
            if count < CLUSTER_MINOR_N:
                continue
            first_ts = rec.get("first_ts") or now
            k = self._key(region, str(olt_id))
            ent = self.active.get(k)
            if not ent:
                # capture FS3900 snapshot at open (optional, safe if unconfigured)
                fs = _fs3900_quick_health(str(olt_id))
                self.active[k] = {
                    "region": region,
                    "olt_id": str(olt_id),
                    "start_ts": float(first_ts),
                    "last_seen_ts": float(now),
                    "peak_count": int(count),
                    "fs3900_at_start": fs,
                }
            else:
                ent["last_seen_ts"] = float(now)
                if count > int(ent.get("peak_count", 0)):
                    ent["peak_count"] = int(count)

    def close_gone(self, alive_keys: set, now: float) -> List[Dict[str, Any]]:
        """
        Close clusters that are not alive anymore OR have gone quiet for CLUSTER_RESTORE_STABLE_S.
        Returns the list of closed records (each appended to the journal).
        """
        closed = []
        for k in list(self.active.keys()):
            ent = self.active[k]
            if k in alive_keys and (now - float(ent.get("last_seen_ts", now))) < CLUSTER_RESTORE_STABLE_S:
                continue  # still alive or not quiet long enough
            start_ts = float(ent.get("start_ts", now))
            end_ts = float(ent.get("last_seen_ts", now))
            rec = {
                "region": ent["region"],
                "olt_id": ent["olt_id"],
                "started_at": start_ts,
                "ended_at": end_ts,
                "duration_s": max(0, int(end_ts - start_ts)),
                "peak_count": int(ent.get("peak_count", 0)),
                "fs3900_at_start": ent.get("fs3900_at_start"),
            }
            try:
                with open(self.journal_path, "a", encoding="utf-8") as f:
                    f.write(json.dumps(rec) + "\n")
            except Exception:
                pass
            del self.active[k]
            closed.append(rec)

        self.save()
        return closed



# ---------------------------
# Staff/NOC + Customer message builders
# ---------------------------

def _build_customer_outage_sms(name_hint: str | None) -> str:
    who = f"{name_hint}," if name_hint else "Hello,"
    return (
        f"{who} we detected a service interruption to your fiber connection. "
        "No action needed. Please *do not* reset equipment unless asked. "
        "We’re monitoring and will update once service is restored."
    )

def _build_customer_restore_sms(name_hint: str | None) -> str:
    who = f"{name_hint}," if name_hint else "Hello,"
    return (
        f"{who} your service shows as restored. "
        "If you still have issues, power-cycle your router once and wait 2–3 minutes. "
        "Reply or call support if the problem continues."
    )

def _build_customer_outage_email(subject_prefix: str = "Service interruption detected") -> Tuple[str, str]:
    subject = subject_prefix
    body = (
        "We detected a temporary service interruption to your fiber connection.\n\n"
        "• No action is required at this time.\n"
        "• Please avoid resetting your equipment unless we ask you to.\n"
        "• We are monitoring and will follow up when service is restored.\n\n"
        "Thank you for your patience."
    )
    return subject, body

def _build_customer_restore_email(subject_prefix: str = "Service restored") -> Tuple[str, str]:
    subject = subject_prefix
    body = (
        "Your service shows as restored.\n\n"
        "If you still experience issues:\n"
        "1) Power-cycle your router once\n"
        "2) Wait 2–3 minutes and re-test\n\n"
        "If problems continue, reply to this message or contact support.\n"
        "Thank you!"
    )
    return subject, body

def _build_staff_outage_summary(*, region: str, olt_label: str, count: int,
                                first_seen: float | None,
                                fs3900: Optional[Dict[str, Any]],
                                uisp_ok: bool) -> Tuple[str, str]:
    subject = f"[OUTAGE] {region.upper()} OLT {olt_label} – {count} endpoints impacted"
    lines = []
    lines.append(f"Region: {region.upper()}")
    lines.append(f"OLT: {olt_label}")
    if first_seen:
        lines.append(f"Start: {_iso_utc(first_seen)}")
    lines.append(f"Endpoints impacted (window): {count}")
    lines.append(f"UISP reachability: {'OK' if uisp_ok else 'UNREACHABLE'}")
    if fs3900:
        lines.append("--- FS3900 quick health ---")
        if 'host' in fs3900:
            lines.append(f"Device: {fs3900.get('host')}")
        down = fs3900.get("ports_down", 0)
        up = fs3900.get("ports_up", 0)
        lines.append(f"Ports up: {up}  down: {down}")
        if fs3900.get("alerts"):
            lines.append("Alerts:")
            for a in fs3900["alerts"]:
                lines.append(f"  - {a}")
    body = "\n".join(lines)
    return subject, body

def _build_staff_restore_summary(*, region: str, olt_label: str,
                                 resolved_at: float | None,
                                 duration_s: Optional[float]) -> Tuple[str, str]:
    subject = f"[RESTORE] {region.upper()} OLT {olt_label} – service normal"
    body = (
        f"Region: {region.upper()}\n"
        f"OLT: {olt_label}\n"
        f"Resolved: {_iso_utc(resolved_at) if resolved_at else 'now'}\n"
        f"Window duration: {_fmt_s(duration_s)}"
    )
    return subject, body


# ---------------------------
# Optional FS3900 quick health
# ---------------------------

def _fs3900_quick_health(olt_id: str) -> Optional[Dict[str, Any]]:
    """
    Returns a small dict for staff messages when fs3900 is configured:
      { host, ports_up, ports_down, alerts: [] }
    Safe no-op if not configured.
    """
    mapping = getattr(settings, "FS3900_BY_OLT", None) or {}
    cfg = mapping.get(str(olt_id)) if isinstance(mapping, dict) else None
    if not cfg or not FS3900Probe:
        return None
    host = cfg.get("host")
    community = cfg.get("community")
    if not host or not community:
        return None

    # ICMP ping first: if the chassis is dead, snapshot won’t help
    alive = _ping_host_icmp(host, count=2, timeout_s=0.8)
    if not alive:
        return {"host": host, "ports_up": 0, "ports_down": 0, "alerts": ["SNMP host not reachable"]}

    try:
        probe = FS3900Probe(host=host, community=community)
        snap = probe.snapshot(sample_traffic=False, traffic_sleep_s=0.8)  # faster pass
        ports = snap.get("ports", []) or []
        up = sum(1 for p in ports if str(p.get("oper")) == "up")
        down = sum(1 for p in ports if str(p.get("oper")) == "down")
        alerts = []
        # Add tiny hints (example: any uplink down?)
        uplinks = [p for p in ports if "1/" in str(p.get("name", ""))]  # heuristic
        if any(str(p.get("oper")) == "down" for p in uplinks):
            alerts.append("Uplink port down")
        return {"host": host, "ports_up": up, "ports_down": down, "alerts": alerts}
    except Exception:
        return {"host": host, "ports_up": 0, "ports_down": 0, "alerts": ["SNAPSHOT error"]}


# ---------------------------
# Cluster engine (tight window)
# ---------------------------

class ClusterEngine:
    """Keep a sliding window of offline events keyed by (region, olt_id)."""

    def __init__(self) -> None:
        self.events: List[Tuple[float, str, str]] = []  # (ts, region, olt)

    def add(self, ts: float, region: str, olt_id: str) -> None:
        self.events.append((ts, region, str(olt_id)))
        self._prune()

    def _prune(self) -> None:
        t = time.time()
        keep = []
        for ts, r, o in self.events:
            if t - ts <= CLUSTER_WINDOW_SEC:
                keep.append((ts, r, o))
        self.events = keep

    def classify(self) -> Dict[Tuple[str, str], Dict[str, Any]]:
        """
        Returns {(region, olt_id): {"count": n, "level": "none|minor|moderate|major", "first_ts": t0}}
        Major = >1 OLT impacted in the window (same region) or counts >= MODERATE
        """
        out: Dict[Tuple[str, str], Dict[str, Any]] = {}
        # Count per (region, olt)
        for ts, r, o in self.events:
            key = (r, o)
            rec = out.setdefault(key, {"count": 0, "first_ts": ts})
            rec["count"] += 1
            if ts < rec["first_ts"]:
                rec["first_ts"] = ts

        # Level
        # per region, how many OLTs have activity?
        region_olts: Dict[str, set] = {}
        for (r, o) in out.keys():
            region_olts.setdefault(r, set()).add(o)

        for k, rec in out.items():
            r, _ = k
            n = rec["count"]
            if len(region_olts.get(r, set())) > 1:
                rec["level"] = "major"
            elif n >= CLUSTER_MODERATE_N:
                rec["level"] = "moderate"
            elif n >= CLUSTER_MINOR_N:
                rec["level"] = "minor"
            else:
                rec["level"] = "none"
        return out

# ---------------------------
# MessageBuilder
# ---------------------------
# --- DROP-IN: MessageBuilder (customer + staff) --------------------
class MessageBuilder:
    """Pure formatting. No I/O. Keep wording here DRY for both email & SMS."""

    @staticmethod
    def _fmt_short_ts(ts: float | None) -> str:
        if not ts:
            return "unknown"
        from datetime import datetime
        return datetime.utcfromtimestamp(ts).strftime("%Y-%m-%d %H:%M UTC")

    # ---------------- Customer messages ----------------
    @staticmethod
    def customer_outage_text(*, street1: str | None, started_at_ts: float | None) -> str:
        place = (street1 or "your location")
        when = MessageBuilder._fmt_short_ts(started_at_ts)
        return (
            f"Heads up: We detected a service interruption at {place}. "
            f"Our team is working on it. Please avoid resetting your equipment unless we contact you. "
            f"Start time: {when}."
        )

    @staticmethod
    def customer_recovery_text(*, street1: str | None) -> str:
        place = (street1 or "your location")
        return (
            f"Good news: Service has been restored at {place}. "
            f"If anything still seems off, please power-cycle your router and wait a few minutes."
        )

    # ---------------- Staff/NOC messages ----------------
    @staticmethod
    def noc_outage_subject(*, region: str, count: int, site_name: str | None = None) -> str:
        sn = f"{site_name} " if site_name else ""
        tier = "Cluster" if count >= 3 else "Single"
        return f"[NOC] {tier} outage {sn}({region.upper()}) – {count} endpoint(s)"

    @staticmethod
    def noc_outage_body_text(
        *,
        region: str,
        site_name: str | None,
        started_at_ts: float | None,
        offline_count: int,
        ping_ok: bool | None,
        fs3900_summary: str | None = None,
    ) -> str:
        lines = []
        lines.append(f"Region: {region.upper()}")
        if site_name:
            lines.append(f"Site:   {site_name}")
        if started_at_ts:
            lines.append(f"Start:  {MessageBuilder._fmt_short_ts(started_at_ts)}")
        lines.append(f"Affected endpoints: {offline_count}")
        if ping_ok is not None:
            lines.append(f"UISP reachability (sanity): {'OK' if ping_ok else 'UNREACHABLE'}")
        if fs3900_summary:
            lines.append("")
            lines.append("FS3900 snapshot:")
            lines.append(fs3900_summary.strip())
        return "\n".join(lines)

    @staticmethod
    def noc_recovery_subject(*, region: str, site_name: str | None = None) -> str:
        sn = f"{site_name} " if site_name else ""
        return f"[NOC] Recovery {sn}({region.upper()})"

    @staticmethod
    def noc_recovery_body_text(
        *,
        region: str,
        site_name: str | None,
        started_at_ts: float | None,
        recovered_at_ts: float | None,
        offline_count_before: int | None = None,
        fs3900_summary: str | None = None,
    ) -> str:
        lines = []
        lines.append(f"Region: {region.upper()}")
        if site_name:
            lines.append(f"Site:   {site_name}")
        if started_at_ts:
            lines.append(f"Started:   {MessageBuilder._fmt_short_ts(started_at_ts)}")
        if recovered_at_ts:
            lines.append(f"Recovered: {MessageBuilder._fmt_short_ts(recovered_at_ts)}")
        if offline_count_before is not None:
            lines.append(f"Prior affected endpoints: {offline_count_before}")
        if fs3900_summary:
            lines.append("")
            lines.append("FS3900 snapshot at recovery:")
            lines.append(fs3900_summary.strip())
        return "\n".join(lines)

# ---------------------------
# Orchestrator
# ---------------------------

class Orchestrator:
    def __init__(self, *, svc: UISPService, state: OutageState, journal_path: str,
                 include_suspended: bool, dry_run: bool, debug: bool,
                 test_client: Optional[str] = None,
                 test_email: Optional[str] = None,
                 test_sms: Optional[str] = None) -> None:
                self.cluster_state = ClusterState(CLUSTER_STATE_PATH, CLUSTER_JOURNAL_PATH)
                self.svc = svc
                self.state = state
                self.journal_path = journal_path
                self.include_suspended = include_suspended
                self.dry = dry_run
                self.debug = debug
                self.test_client = test_client
                self.test_email = test_email
                self.test_sms = test_sms
                self.cluster = ClusterEngine()
                # UISP reachability cache to avoid hammering
                self._uisp_host = _extract_host(settings.NSM_BASE_URL)
                self._uisp_ok_cache_ts = 0.0
                self._uisp_ok_cached = True

    # ---- polling ----
    def poll_offline_rows(self, *, regions: list[str]) -> list[dict]:
        """
        STRICT site-driven detector.
        For each region:
          - load its region site (LOCATIONS[region]['site'])
          - iterate description.endpoints[]
          - if endpoint.status == 'disconnected':
                suspended = bool(endpoint.suspended is True)
                include if (include_suspended or not suspended)
          - build rows with endpoint id as device_id + site_id (stable key)
          - last_seen/age come from endpoint.updated (ISO)
          - olt_id is left '' in site-only mode
        """
        rows: list[dict] = []
        locs: dict = getattr(settings, "LOCATIONS", {}) or {}
        dbg_counts: list[str] = []

        def _iso_age(iso: str | None) -> tuple[str, float | None]:
            from datetime import datetime, timezone
            if not iso:
                return "", None
            try:
                dt = datetime.fromisoformat(iso.replace("Z", "+00:00"))
                age_s = (datetime.now(timezone.utc) - dt).total_seconds()
                # for printing, keep ISO trimmed
                return iso[:19], max(age_s, 0.0)
            except Exception:
                return str(iso), None

        for region in regions:
            cfg = locs.get(region, {}) if isinstance(locs, dict) else {}
            region_site_id = str(cfg.get("site") or "").strip()
            if not region_site_id:
                if self.debug:
                    print(f"[SITE] region={region} has no configured 'site' id; skipping")
                continue

            # pull endpoints once
            endpoints = self.svc.nsm.list_endpoints_for_site(region_site_id)
            if self.debug:
                print(f"[SITE] region={region} site={region_site_id} endpoints={len(endpoints)}")
            dbg_counts.append(f"{region}:{region_site_id} -> {len(endpoints)}")

            for ep in endpoints:
                if not isinstance(ep, dict):
                    continue

                ep_id = str(ep.get("id") or "").strip()
                if not ep_id:
                    # never emit blank device_id — skip
                    continue

                status = str(ep.get("status") or "").lower().strip()
                suspended = bool(ep.get("suspended") is True)

                # only care about real disconnected
                if status != "disconnected":
                    # we also ignore 'inactive' by design
                    continue

                if suspended and not self.include_suspended:
                    if self.debug:
                        print(f"DEBUG skip suspended endpoint={ep_id} region={region}")
                    continue

                last_seen_iso = str(ep.get("updated") or "") or ""
                last_seen_trim, age_s = _iso_age(last_seen_iso)

                rows.append({
                    "region": region,
                    "device_id": ep_id,  # <- use endpoint id as stable key
                    "olt_id": "",  # <- unknown in site-only mode
                    "site_id": ep_id,  # <- endpoint IS a child site id
                    "status": "disconnected",
                    "suspended": suspended,
                    "reason": ("suspended" if suspended else "offline"),
                    "last_seen": last_seen_trim,
                    "age_s": age_s,
                })

        if self.debug:
            hdr = "; ".join(dbg_counts) if dbg_counts else "none"
            print(
                f"OFFLINE ONUs (disconnected/offline; suspended {'included' if self.include_suspended else 'excluded'} )")
            print(f"Regions: {','.join(regions)}  |  Site counts: {hdr}")
            print(f"Found: {len(rows)}")
        return rows

    # ---- UISP host reachability ----

    def _uisp_reachable(self) -> bool:
        now = time.time()
        # cache for ~15 seconds
        if now - self._uisp_ok_cache_ts <= 15.0:
            return self._uisp_ok_cached
        ok = _ping_host_icmp(self._uisp_host, count=2, timeout_s=0.8)
        self._uisp_ok_cached = ok
        self._uisp_ok_cache_ts = now
        return ok

    # ---- journal ----

    def _append_journal(self, *, device_id: str, st: DeviceState) -> None:
        try:
            if not st.first_offline_at or not st.recovered_at:
                return
            dur = st.recovered_at - st.first_offline_at
            if dur < MIN_OUTAGE_FOR_JOURNAL_S:
                return
            os.makedirs(os.path.dirname(self.journal_path) or ".", exist_ok=True)
            rec = {
                "device_id": device_id,
                "olt_id": st.olt_id,
                "site_id": st.site_id,
                "client_id": st.client_id,
                "started_at": st.first_offline_at,
                "recovered_at": st.recovered_at,
                "duration_s": int(dur),
            }
            with open(self.journal_path, "a", encoding="utf-8") as f:
                f.write(json.dumps(rec) + "\n")
        except Exception as e:
            if self.debug:
                print(f"[JOURNAL] append error: {e}")

    # ---- notifications ----
    def _send_customer_outage(self, st: DeviceState) -> None:
        subj, body = _build_customer_outage_email()
        # Email recipient
        email_to = self.test_email or st.client_email
        sms_to = self.test_sms or st.client_phone
        if email_to and st.client_email_ok:
            if self.dry:
                print(f"[EMAIL] (dry) to={email_to} subject={subj}")
            else:
                notify.send_email(email_to, subj, body)
        # SMS
        if sms_to and st.client_sms_ok:
            sms = _build_customer_outage_sms(None)
            if self.dry:
                print(f"[SMS] (dry) to={sms_to} msg={sms}")
            else:
                notify.send_sms_raw(sms_to, sms)

    def _send_customer_restore(self, st: DeviceState) -> None:
        subj, body = _build_customer_restore_email()
        email_to = self.test_email or st.client_email
        sms_to = self.test_sms or st.client_phone
        if email_to and st.client_email_ok:
            if self.dry:
                print(f"[EMAIL] (dry) to={email_to} subject={subj}")
            else:
                notify.send_email(email_to, subj, body)
        if sms_to and st.client_sms_ok:
            sms = _build_customer_restore_sms(None)
            if self.dry:
                print(f"[SMS] (dry) to={sms_to} msg={sms}")
            else:
                notify.send_sms_raw(sms_to, sms)

    def _send_staff_outage(self, *, region: str, olt_id: str, count: int, first_seen: float | None) -> None:
        fs = _fs3900_quick_health(olt_id)
        uisp_ok = self._uisp_reachable()
        olt_label = olt_id
        subj, body = _build_staff_outage_summary(region=region, olt_label=olt_label, count=count,
                                                 first_seen=first_seen, fs3900=fs, uisp_ok=uisp_ok)
        to_email = self.test_email or getattr(settings, "NOC_EMAIL", None)
        to_sms = self.test_sms or getattr(settings, "NOC_SMS", None)
        if to_email:
            if self.dry:
                print(f"[NOC EMAIL] (dry) to={to_email} subject={subj}")
            else:
                notify.send_email(to=to_email, subject=subj, body=body)
        if to_sms:
            if self.dry:
                print(f"[NOC SMS] (dry) to={to_sms} msg={subj}")
            else:
                notify.send_sms(to=to_sms, message=subj)

    def _send_staff_restore(self, *, region: str, olt_id: str, t0: float | None, t1: float | None) -> None:
        dur = (t1 - t0) if (t0 and t1) else None
        subj, body = _build_staff_restore_summary(region=region, olt_label=olt_id, resolved_at=t1, duration_s=dur)
        to_email = self.test_email or getattr(settings, "NOC_EMAIL", None)
        to_sms = self.test_sms or getattr(settings, "NOC_SMS", None)
        if to_email:
            if self.dry:
                print(f"[NOC EMAIL] (dry) to={to_email} subject={subj}")
            else:
                notify.send_email(to=to_email, subject=subj, body=body)
        if to_sms:
            if self.dry:
                print(f"[NOC SMS] (dry) to={to_sms} msg={subj}")
            else:
                notify.send_sms(to=to_sms, message=subj)

    # ---- one orchestration pass ----

    def orchestrate_once(self, *, regions: List[str]) -> None:
        now = time.time()
        rows = self.poll_offline_rows(regions=regions)
        # mark seen devices and feed cluster engine
        seen = set()
        for r in rows:
            did = r["device_id"]
            seen.add(did)
            if not r.get("suspended"):
                self.cluster.add(now, r["region"], r["olt_id"])

        # 1) Handle offline devices (start timers, open tickets after threshold)
        for r in rows:
            did = r["device_id"]
            st = self.state.get(did)
            if not st:
                st = DeviceState(
                    device_id=did,
                    olt_id=r["olt_id"],
                    site_id=r["site_id"],
                    suspended=bool(r.get("suspended")),
                )
            st.last_seen_poll = now
            if not st.first_offline_at:
                # Fresh outage
                st.first_offline_at = now
                # Attempt CRM mapping only if we don’t already have a client and not suspended
                if not st.suspended:
                    # Prefer live CRM mapping if available; otherwise keep test override
                    if self.test_client:
                        st.client_id = str(self.test_client)
                    if not st.client_id:
                        # Keep it simple: your UISPService.CRMClient has get_client(id) –
                        # mapping from ONU→client runs through site; you already tested this path.
                        pass
                self.state.set(st)
                print(f"[STATE] OFFLINE start device={did} olt={st.olt_id} site={st.site_id} client={st.client_id}")

            # After threshold, open ticket + notify customer once
            if not st.had_ticket and not st.suspended:
                age = now - (st.first_offline_at or now)
                if age >= OPEN_TICKET_AFTER_S:
                    # Create ticket
                    if self.dry:
                        print(f"[TICKET] (dry) create for device={did}")
                        st.ticket_id = st.ticket_id or f"SIM-{int(now)}"
                        st.had_ticket = True
                    else:
                        # Build minimal payload via CRM client (uses working path in your UISPService)
                        subject = "Service interruption detected"
                        text = f"Automated: ONU connectivity loss detected at {_iso_utc(st.first_offline_at)} UTC."
                        tid = self.svc.crm.create_ticket(subject=subject, client_id=st.client_id, body_text=text)
                        if tid:
                            print(f"[TICKET] created id={tid}")
                            st.ticket_id = str(tid)
                            st.had_ticket = True
                        else:
                            print("[TICKET] create FAILED")

                    # Customer notices (friendlier copy)
                    self._send_customer_outage(st)
                    self.state.set(st)

        # 2) Handle recovered devices (in state but not seen this pass)
        to_close: List[str] = []
        for did, st in list(self.state.items()):
            if did in seen:
                continue  # still offline
            # consider simulation “ignore until” window
            if st.sim_ignore_until and now < st.sim_ignore_until:
                continue

            # Recovery detection
            if st.first_offline_at:
                if not st.recovered_at:
                    st.recovered_at = now
                    self.state.set(st)
                    continue  # start recovery grace

                # After grace, close ticket & notify
                if now - st.recovered_at >= CLOSE_AFTER_RECOVERY_S:
                    # Patch & close ticket
                    if st.had_ticket and st.ticket_id:
                        note = f"Automated: Service restored at {_iso_utc(st.recovered_at)} UTC."
                        if self.dry:
                            print(f"[TICKET] (dry) close id={st.ticket_id} note='{note}'")
                        else:
                            self.svc.crm.update_ticket(ticket_id=str(st.ticket_id), note=note, status="closed")
                            print(f"[TICKET] closed id={st.ticket_id}")

                    # Customer restore (no outage duration in customer copy)
                    self._send_customer_restore(st)

                    # Journal
                    self._append_journal(device_id=did, st=st)

                    to_close.append(did)

        for did in to_close:
            self.state.remove(did)

        # 3) Cluster classification → Staff/NOC signal
        cls = self.cluster.classify()
        # Suppress cluster if UISP host is not reachable
        uisp_ok = self._uisp_reachable()
        # Persist cluster episodes for reporting (independent of notifications)
        self.cluster_state.update_from_classification(cls, now)
        alive_minor_or_higher = {
            f"{r}|{o}" for (r, o), rec in cls.items() if int(rec.get("count", 0)) >= CLUSTER_MINOR_N
        }
        closed = self.cluster_state.close_gone(alive_minor_or_higher, now)
        # (Optional) you could send a NOC "recovery" notice here per closed episode if desired.

        if uisp_ok:
            for (region, olt_id), rec in cls.items():
                level = rec.get("level")
                if level in ("minor", "moderate", "major"):
                    cnt = rec.get("count", 0)
                    t0 = rec.get("first_ts")
                    self._send_staff_outage(region=region, olt_id=olt_id, count=cnt, first_seen=t0)
        else:
            if self.debug:
                print("[CLUSTER] UISP reachability failed; suppressing cluster alerts")

        # Final: persist state
        self.state.save()

    # ---- Helpers ----

    def _classify_onu_status(self, dev: dict) -> Tuple[str, str, str]:
        """
        Conservative ONU classifier (used only outside the main poller if needed).

        Returns: (status, connected_str, reason)
          status: 'disconnected' or 'connected' or ''
          connected_str: 'true'/'false'/'' (string for logging)
          reason: lowercased reason string if present
        """
        s_top = str(dev.get("status") or "").lower().strip()
        ov = dev.get("overview") or {}
        s_ov = str(ov.get("status") or "").lower().strip()
        conn = ov.get("connected")

        status = ""
        if s_top == "disconnected" or s_ov == "disconnected":
            status = "disconnected"
        elif conn is False:
            status = "disconnected"
        elif s_top or s_ov:
            status = s_top or s_ov

        reason = str(ov.get("disconnectReason") or "").lower().strip()
        return status, ("false" if conn is False else ("true" if conn is True else "")), reason

    # ---- CLI helpers ----

    def simulate_recover(self, *, device_id: str, age_s: Optional[int] = None) -> None:
        st = self.state.get(device_id)
        if not st:
            print(f"[SIM] device {device_id} not in state; nothing to recover")
            return
        now = time.time()
        st.recovered_at = now
        # for stability, give watcher some time gap before treating as “fully recovered”
        st.sim_ignore_until = now + (age_s or 30)
        self.state.set(st)
        self.state.save()
        print(f"[SIM] {device_id} marked recovered_at={st.recovered_at} (ignore until {st.sim_ignore_until})")

    def crm_update(self, *, ticket_id: str, note: str, status: Optional[str], send_real: bool) -> None:
        if not send_real:
            print(f"[TICKET] (dry) update id={ticket_id} note='{note}'")
            print(">> CRM update OK")
            return
        self.svc.crm.update_ticket(ticket_id=ticket_id, note=note, status=status)
        print(">> CRM update OK")


# ---------------------------
# File lock (avoid overlap)
# ---------------------------

class FileLock:
    def __init__(self, path: str) -> None:
        self.path = path
        os.makedirs(os.path.dirname(self.path) or ".", exist_ok=True)
        self._fd = None

    def acquire(self) -> bool:
        try:
            self._fd = os.open(self.path, os.O_CREAT | os.O_EXCL | os.O_WRONLY)
            os.write(self._fd, str(os.getpid()).encode("utf-8"))
            return True
        except FileExistsError:
            return False
        except Exception:
            return False

    def release(self) -> None:
        try:
            if self._fd is not None:
                os.close(self._fd)
            if os.path.exists(self.path):
                os.remove(self.path)
        except Exception:
            pass


# ---------------------------
# CLI
# ---------------------------

def _resolve_regions() -> List[str]:
    # CLI can override by passing a CSV after subcommand; otherwise REGION_KEYS or LOCATIONS keys
    raw = str(getattr(settings, "REGION_KEYS", "")).strip()
    if raw:
        return [r.strip().lower() for r in raw.split(",") if r.strip()]
    locs = getattr(settings, "LOCATIONS", {})
    if isinstance(locs, dict) and locs:
        return sorted([str(k).lower() for k in locs.keys()])
    return []

def main(argv: List[str]) -> None:
    if not argv:
        print(">> usage:")
        print("  python probe_and_notify.py poll [--debug] [--exclude-suspended|--include-suspended]")
        print("  python probe_and_notify.py watch [--interval N] [--state PATH] [--journal PATH] [--no-dry] [--debug] [--exclude-suspended|--include-suspended] [--test-client ID] [--test-email X] [--test-sms X]")
        print("  python probe_and_notify.py simulate recover --device ID [--state PATH] [--age N]")
        print("  python probe_and_notify.py crm update --id TICKET_ID --note TEXT [--status STATUS] [--send] [--debug]")
        return

    print(">> entering probe_and_notify __main__")
    print(f">> argv: {argv}")

    sub = argv[0].lower()

    # Common flags
    include_suspended = DEFAULT_INCLUDE_SUSPENDED
    debug = ("--debug" in argv)
    dry = ("--no-dry" not in argv)  # default: dry; add --no-dry for real actions
    state_path = STATE_PATH
    journal_path = JOURNAL_PATH
    interval = 10
    test_client = None
    test_email = None
    test_sms = None

    if "--exclude-suspended" in argv or "--no-suspended" in argv:
        include_suspended = False
    if "--include-suspended" in argv or "--suspended" in argv:
        include_suspended = True
    if "--state" in argv:
        try:
            state_path = argv[argv.index("--state") + 1]
        except Exception:
            pass
    if "--journal" in argv:
        try:
            journal_path = argv[argv.index("--journal") + 1]
        except Exception:
            pass
    if "--interval" in argv:
        try:
            interval = int(argv[argv.index("--interval") + 1])
        except Exception:
            interval = 10
    if "--test-client" in argv:
        try:
            test_client = argv[argv.index("--test-client") + 1]
        except Exception:
            test_client = None
    if "--test-email" in argv:
        try:
            test_email = argv[argv.index("--test-email") + 1]
        except Exception:
            test_email = None
    if "--test-sms" in argv:
        try:
            test_sms = argv[argv.index("--test-sms") + 1]
        except Exception:
            test_sms = None

    # Service
    nsm = NSMClient(settings.NSM_BASE_URL, settings.NSM_TOKEN, debug=debug)
    crm = CRMClient(settings.CRM_BASE_URL, settings.CRM_TOKEN, debug=debug)
    svc = UISPService(nsm=nsm, crm=crm)
    state = OutageState(state_path)

    # Subcommands
    if sub == "poll":
        regions = _resolve_regions()
        orch = Orchestrator(svc=svc, state=state, journal_path=journal_path,
                            include_suspended=include_suspended,
                            dry_run=dry, debug=debug,
                            test_client=test_client, test_email=test_email, test_sms=test_sms)
        rows = orch.poll_offline_rows(regions=regions)
        print(f"OFFLINE ONUs (disconnected/offline; suspended {'included' if include_suspended else 'excluded'} )")
        # Debug OLT counts
        counts: Dict[Tuple[str, str], int] = {}
        for r in rows:
            key = (r["region"], r["olt_id"])
            counts[key] = counts.get(key, 0) + 1
        if counts:
            parts = [f"{reg}:{olt} -> {n}" for (reg, olt), n in counts.items()]
            print("Regions:", ",".join(regions), " |  OLT counts:", "; ".join(parts))
        else:
            print("Regions:", ",".join(regions), " |  OLT counts: none")
        print(f"Found: {len(rows)}")
        if rows:
            # header
            print(
                f"{'REGION':<6} {'DEVICE_ID':<36} {'OLT':<36} {'SITE':<36} {'STATUS':<12} {'REASON':<10} {'AGE':<8} {'LAST_SEEN':<20}")
            print("-" * 150)
            for r in rows:
                status_out = r.get("status", "")
                reason = r.get("reason", "offline")
                age = _fmt_s(r.get("age_s"))
                ls = str(r.get("last_seen") or "")[:19]
                print(
                    f"{r['region']:<6} {r['device_id']:<36} {r['olt_id']:<36} {r['site_id']:<36} {status_out:<12} {reason:<10} {age:<8} {ls:<20}")
        return

    if sub == "watch":
        regions = _resolve_regions()
        print(f">> watch regions: {regions} interval={interval}s dry={'True' if dry else 'False'} debug={debug} susp={'include' if include_suspended else 'exclude'} state={state_path}")
        lock = FileLock(LOCK_PATH)
        if not lock.acquire():
            print(">> another watcher appears to be running (lock busy); exiting")
            return
        try:
            orch = Orchestrator(svc=svc, state=state, journal_path=journal_path,
                                include_suspended=include_suspended,
                                dry_run=dry, debug=debug,
                                test_client=test_client, test_email=test_email, test_sms=test_sms)
            while True:
                try:
                    orch.orchestrate_once(regions=regions)
                except Exception as e:
                    print(f"[watch] error: {e}")
                time.sleep(interval)
        finally:
            lock.release()
        return

    if sub == "simulate" and len(argv) >= 2 and argv[1].lower() == "recover":
        try:
            device_id = argv[argv.index("--device") + 1]
        except Exception:
            print(">> usage: python probe_and_notify.py simulate recover --device ID [--state PATH] [--age N]")
            return
        age = None
        if "--age" in argv:
            try:
                age = int(argv[argv.index("--age") + 1])
            except Exception:
                age = None
        # reuse current state path
        orch = Orchestrator(svc=svc, state=state, journal_path=journal_path,
                            include_suspended=include_suspended,
                            dry_run=dry, debug=debug,
                            test_client=test_client, test_email=test_email, test_sms=test_sms)
        orch.simulate_recover(device_id=device_id, age_s=age)
        return

    if sub == "crm" and len(argv) >= 2 and argv[1].lower() == "update":
        try:
            ticket_id = argv[argv.index("--id") + 1]
            note = argv[argv.index("--note") + 1]
        except Exception:
            print(">> usage: python probe_and_notify.py crm update --id TICKET_ID --note TEXT [--status STATUS] [--send] [--debug]")
            return
        status = None
        if "--status" in argv:
            try:
                status = argv[argv.index("--status") + 1]
            except Exception:
                status = None
        send_real = ("--send" in argv)
        orch = Orchestrator(svc=svc, state=state, journal_path=journal_path,
                            include_suspended=include_suspended,
                            dry_run=(not send_real and dry), debug=debug)
        orch.crm_update(ticket_id=ticket_id, note=note, status=status, send_real=send_real)
        return

    print(">> usage: unknown subcommand")
    return


if __name__ == "__main__":
    main(sys.argv[1:])
